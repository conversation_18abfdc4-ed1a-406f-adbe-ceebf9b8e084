#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QIcon>
#include <QtQml>
#include "core/DataDiscoveryEngine.h"
#include "core/RiskAssessmentEngine.h"
#pragma comment(lib, "user32.lib")

int main(int argc, char *argv[])
{
    QGuiApplication app(argc, argv);

    // 设置应用程序信息
    app.setApplicationName("数据安全与隐私管理平台");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("Data Protection Platform");

    // 注册C++类型到QML
    qmlRegisterType<DataDiscoveryEngine>("DataProtection", 1, 0, "DataDiscoveryEngine");
    qmlRegisterType<RiskAssessmentEngine>("DataProtection", 1, 0, "RiskAssessmentEngine");

    QQmlApplicationEngine engine;

    // 创建数据发现引擎实例并注册为全局对象
    DataDiscoveryEngine* dataDiscoveryEngine = new DataDiscoveryEngine(&app);
    engine.rootContext()->setContextProperty("dataDiscoveryEngine", dataDiscoveryEngine);

    // 创建风险评估引擎实例并注册为全局对象
    RiskAssessmentEngine* riskAssessmentEngine = new RiskAssessmentEngine(&app);
    engine.rootContext()->setContextProperty("riskAssessmentEngine", riskAssessmentEngine);

    // 连接风险评估引擎与数据发现引擎
    riskAssessmentEngine->setDataDiscoveryEngine(dataDiscoveryEngine);

    // 加载主QML文件
    const QUrl url(QStringLiteral("qrc:/src/qml/main.qml"));
    QObject::connect(&engine, &QQmlApplicationEngine::objectCreated,
                     &app, [url](QObject *obj, const QUrl &objUrl) {
        if (!obj && url == objUrl)
            QCoreApplication::exit(-1);
    }, Qt::QueuedConnection);

    engine.load(url);

    return app.exec();
}