cmake_minimum_required(VERSION 3.5) # CMake install : https://cmake.org/download/
project(main_window LANGUAGES CXX)
set(CMAKE_INCLUDE_CURRENT_DIR ON)
set(CMAKE_PREFIX_PATH "C:/Qt/Qt6/6.5.3/msvc2019_64") # Qt Kit Dir
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(ENV{QML2_IMPORTS_PATH} "${CMAKE_PREFIX_PATH}/qml")
find_package(Qt6 COMPONENTS Widgets Qml Quick REQUIRED) # Qt COMPONENTS
aux_source_directory(./src srcs)
aux_source_directory(./src/core core_srcs)

# Specify MSVC UTF-8 encoding
add_compile_options("$<$<C_COMPILER_ID:MSVC>:/utf-8>")
add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")

add_executable(${PROJECT_NAME}
    # 使用debug输出
    # WIN32
    ${srcs}
    ${core_srcs}
    resources.qrc
)

# 检查 QML 文件是否存在
if(NOT EXISTS ${CMAKE_SOURCE_DIR}/src/qml/main.qml)
    message(FATAL_ERROR "QML file main.qml not found!")
endif()

target_link_libraries(${PROJECT_NAME} PRIVATE Qt6::Widgets Qt6::Qml Qt6::Quick) # Qt6 Shared Library