pragma ComponentBehavior: Bound

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs

ScrollView {
    id: root
    anchors.fill: parent
    contentWidth: availableWidth
    contentHeight: mainColumn.implicitHeight

    // 确保滚动条始终可见且可用
    ScrollBar.vertical.policy: ScrollBar.AsNeeded
    ScrollBar.horizontal.policy: ScrollBar.AsNeeded
    clip: true

    property var filteredRules: []

    // 更新规则列表的函数
    function updateRuleList() {
        try {
            console.log("开始更新规则列表...");
            var allRules = dataDiscoveryEngine.getScanRules();
            console.log("获取到规则数量:", allRules.length);
            var filterIndex = ruleFilterComboBox.currentIndex;

            if (filterIndex === 0) {
                // 所有规则
                filteredRules = allRules;
            } else {
                // 根据数据类型筛选
                var targetDataType = filterIndex - 1;  // 0:PII, 1:财务, 2:健康, 3:商业机密, 4:其他
                filteredRules = allRules.filter(function (rule) {
                    return rule.dataType === targetDataType;
                });
            }

            ruleListView.model = filteredRules;
            console.log("规则列表更新完成，筛选后规则数量:", filteredRules.length);
        } catch (error) {
            console.error("更新规则列表时发生错误:", error);
        }
    }

    Component.onCompleted: {
        updateRuleList();
        // 连接规则变化信号，使用Qt.QueuedConnection避免死锁
        dataDiscoveryEngine.scanRulesChanged.connect(function () {
            Qt.callLater(root.updateRuleList);
        });
    }

    ColumnLayout {
        id: mainColumn
        width: parent.width
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.margins: 20
        spacing: 20

        // 页面标题
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")

            RowLayout {
                anchors.fill: parent
                anchors.margins: 8

                Text {
                    text: "🔍 数据发现与分类"
                    font.pixelSize: 24
                    font.bold: true
                    color: Qt.color("#2c3e50")
                }

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: dataDiscoveryEngine.isScanning ? "停止扫描" : "开始全面扫描"
                    Material.background: dataDiscoveryEngine.isScanning ? Qt.color("#e74c3c") : Qt.color("#3498db")
                    Material.foreground: Qt.color("white")
                    onClicked: {
                        if (dataDiscoveryEngine.isScanning) {
                            dataDiscoveryEngine.stopScan();
                        } else {
                            dataDiscoveryEngine.startScan(scanPathField.text, includeSubdirsCheckBox.checked, scanHiddenCheckBox.checked, scanSystemCheckBox.checked);
                        }
                    }
                }
            }
        }

        // 本地文件扫描区域
        GroupBox {
            Layout.fillWidth: true
            title: "本地文件扫描"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                RowLayout {
                    Layout.fillWidth: true

                    TextField {
                        id: scanPathField
                        Layout.fillWidth: true
                        placeholderText: "选择要扫描的目录路径..."
                        text: "C:\\"
                    }

                    Button {
                        text: "浏览"
                        onClicked: {
                            folderDialog.open();
                        }
                    }

                    Button {
                        text: dataDiscoveryEngine.isScanning ? "停止扫描" : "开始扫描"
                        Material.background: dataDiscoveryEngine.isScanning ? Qt.color("#e74c3c") : Qt.color("#27ae60")
                        Material.foreground: Qt.color("white")
                        enabled: scanPathField.text.length > 0
                        onClicked: {
                            if (dataDiscoveryEngine.isScanning) {
                                dataDiscoveryEngine.stopScan();
                            } else {
                                dataDiscoveryEngine.startScan(scanPathField.text, includeSubdirsCheckBox.checked, scanHiddenCheckBox.checked, scanSystemCheckBox.checked);
                            }
                        }
                    }
                }

                RowLayout {
                    Layout.fillWidth: true

                    CheckBox {
                        id: includeSubdirsCheckBox
                        text: "包含子目录"
                        checked: true
                    }

                    CheckBox {
                        id: scanHiddenCheckBox
                        text: "扫描隐藏文件"
                        checked: false
                    }

                    CheckBox {
                        id: scanSystemCheckBox
                        text: "扫描系统文件"
                        checked: false
                    }
                }

                // 扫描进度
                ProgressBar {
                    Layout.fillWidth: true
                    value: dataDiscoveryEngine.scanProgress / 100.0
                    visible: dataDiscoveryEngine.isScanning
                }

                Text {
                    text: dataDiscoveryEngine.isScanning ? "扫描状态: 正在扫描... " + dataDiscoveryEngine.scanProgress + "% - " + dataDiscoveryEngine.currentScanFile : "扫描状态: 就绪"
                    color: Qt.color("#7f8c8d")
                }
            }
        }

        // 规则引擎分类区域
        GroupBox {
            Layout.fillWidth: true
            title: "规则引擎分类"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                RowLayout {
                    Layout.fillWidth: true

                    ComboBox {
                        id: ruleFilterComboBox
                        Layout.preferredWidth: 200
                        model: ["所有规则", "PII规则", "财务数据规则", "健康数据规则", "商业机密规则", "其他规则"]
                        currentIndex: 0
                        onCurrentIndexChanged: {
                            updateRuleList();
                        }
                    }

                    Button {
                        text: "管理规则"
                        onClicked: {
                            ruleManagementDialog.open();
                        }
                    }

                    Button {
                        text: "新建规则"
                        Material.background: Qt.color("#f39c12")
                        Material.foreground: Qt.color("white")
                        onClicked: {
                            // 使用通用组件创建新建规则对话框
                            var dialog = ruleEditComponent.createObject(root, {
                                "dialogTitle": "新建扫描规则",
                                "isEditMode": false
                            });

                            // 连接信号
                            dialog.ruleAccepted.connect(function (name, pattern, dataType, riskLevel, enabled, description, originalName) {
                                console.log("新建规则:", name);
                                Qt.callLater(function () {
                                    try {
                                        dataDiscoveryEngine.addScanRule(name, pattern, dataType, riskLevel, enabled, description);
                                        console.log("规则创建完成");
                                    } catch (error) {
                                        console.error("创建规则时发生错误:", error);
                                    }
                                });
                                dialog.destroy();
                            });

                            dialog.ruleCancelled.connect(function () {
                                dialog.destroy();
                            });

                            // 清空表单并打开对话框
                            dialog.clearForm();
                            dialog.open();
                        }
                    }

                    Button {
                        text: "重新加载规则"
                        Material.background: Qt.color("#9b59b6")
                        Material.foreground: Qt.color("white")
                        onClicked: {
                            dataDiscoveryEngine.loadRulesFromFile();
                            updateRuleList();
                        }
                    }
                }

                // 规则列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.min(400, Math.max(200, ruleListView.contentHeight + 16))
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: ruleListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: root.filteredRules
                        clip: true

                        delegate: Rectangle {
                            id: ruleDelegateItem
                            required property int index
                            required property var modelData
                            width: parent ? parent.width : 0
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                CheckBox {
                                    checked: ruleDelegateItem.modelData.enabled
                                    onCheckedChanged: {
                                        dataDiscoveryEngine.updateScanRule(ruleDelegateItem.modelData.name, ruleDelegateItem.modelData.pattern, ruleDelegateItem.modelData.dataType, ruleDelegateItem.modelData.riskLevel, checked, ruleDelegateItem.modelData.description);
                                    }
                                }

                                Text {
                                    Layout.preferredWidth: 100
                                    text: ruleDelegateItem.modelData.name
                                    font.bold: true
                                }

                                Text {
                                    Layout.fillWidth: true
                                    text: ruleDelegateItem.modelData.pattern
                                    color: Qt.color("#7f8c8d")
                                    font.family: "Consolas"
                                    elide: Text.ElideRight
                                }

                                Text {
                                    Layout.preferredWidth: 60
                                    text: ruleDelegateItem.modelData.dataTypeName
                                    color: Qt.color("#3498db")
                                    font.pixelSize: 12
                                }

                                Text {
                                    Layout.preferredWidth: 60
                                    text: ruleDelegateItem.modelData.riskLevelName
                                    color: ruleDelegateItem.modelData.riskLevel === 0 ? Qt.color("#e74c3c") : ruleDelegateItem.modelData.riskLevel === 1 ? Qt.color("#f39c12") : Qt.color("#27ae60")
                                    font.pixelSize: 12
                                }
                            }
                        }
                    }
                }
            }
        }

        // 敏感信息统计区域
        GroupBox {
            Layout.fillWidth: true
            title: "敏感信息统计区域"

            ColumnLayout {
                anchors.fill: parent
                spacing: 16

                // 总敏感信息统计
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 60
                    border.color: Qt.color("#3498db")
                    border.width: 2
                    radius: 6
                    color: Qt.color("#ecf0f1")

                    RowLayout {
                        anchors.centerIn: parent
                        spacing: 12

                        Text {
                            text: "总敏感信息数量:"
                            font.bold: true
                            font.pixelSize: 16
                            color: Qt.color("#2c3e50")
                        }

                        Text {
                            text: dataDiscoveryEngine.scanStatistics.totalSensitiveItems + " 项"
                            font.bold: true
                            font.pixelSize: 18
                            color: Qt.color("#e74c3c")
                        }
                    }
                }

                // 按规则分类统计
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 120
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 12
                        spacing: 8

                        Text {
                            text: "按规则分类统计"
                            font.bold: true
                            font.pixelSize: 14
                            color: Qt.color("#2c3e50")
                        }

                        ColumnLayout {
                            Layout.fillWidth: true
                            spacing: 12

                            // 使用Grid布局确保完美对齐
                            Grid {
                                Layout.fillWidth: true
                                columns: 5
                                columnSpacing: 0
                                rowSpacing: 12

                                // 计算每列的宽度
                                property real columnWidth: (parent.width) / 5

                                // 标题行
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: "PII数据"
                                        font.bold: true
                                        color: Qt.color("#2c3e50")
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: "财务数据"
                                        font.bold: true
                                        color: Qt.color("#2c3e50")
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: "健康数据"
                                        font.bold: true
                                        color: Qt.color("#2c3e50")
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: "商业机密"
                                        font.bold: true
                                        color: Qt.color("#2c3e50")
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: "其他敏感"
                                        font.bold: true
                                        color: Qt.color("#2c3e50")
                                    }
                                }

                                // 数据行
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: dataDiscoveryEngine.scanStatistics.piiCount + " 项"
                                        color: Qt.color("#e74c3c")
                                        font.bold: true
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: dataDiscoveryEngine.scanStatistics.financialCount + " 项"
                                        color: Qt.color("#f39c12")
                                        font.bold: true
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: dataDiscoveryEngine.scanStatistics.healthCount + " 项"
                                        color: Qt.color("#e67e22")
                                        font.bold: true
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: dataDiscoveryEngine.scanStatistics.businessSecretCount + " 项"
                                        color: Qt.color("#9b59b6")
                                        font.bold: true
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: dataDiscoveryEngine.scanStatistics.otherCount + " 项"
                                        color: Qt.color("#95a5a6")
                                        font.bold: true
                                    }
                                }
                            }
                        }
                    }
                }

                // 按风险等级统计
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 12
                        spacing: 8

                        Text {
                            text: "按风险等级统计"
                            font.bold: true
                            font.pixelSize: 14
                            color: Qt.color("#2c3e50")
                        }

                        ColumnLayout {
                            Layout.fillWidth: true
                            spacing: 12

                            // 使用Grid布局确保完美对齐
                            Grid {
                                Layout.fillWidth: true
                                columns: 3
                                columnSpacing: 0
                                rowSpacing: 12

                                // 计算每列的宽度
                                property real columnWidth: (parent.width) / 3

                                // 标题行
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: "高风险"
                                        font.bold: true
                                        color: Qt.color("#2c3e50")
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: "中风险"
                                        font.bold: true
                                        color: Qt.color("#2c3e50")
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: "低风险"
                                        font.bold: true
                                        color: Qt.color("#2c3e50")
                                    }
                                }

                                // 数据行
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: dataDiscoveryEngine.scanStatistics.highRiskCount + " 项"
                                        color: Qt.color("#e74c3c")
                                        font.bold: true
                                        font.pixelSize: 14
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: dataDiscoveryEngine.scanStatistics.mediumRiskCount + " 项"
                                        color: Qt.color("#f39c12")
                                        font.bold: true
                                        font.pixelSize: 14
                                    }
                                }
                                Rectangle {
                                    width: parent.columnWidth
                                    height: 20
                                    color: Qt.color("transparent")
                                    Text {
                                        anchors.centerIn: parent
                                        text: dataDiscoveryEngine.scanStatistics.lowRiskCount + " 项"
                                        color: Qt.color("#27ae60")
                                        font.bold: true
                                        font.pixelSize: 14
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Item {
            Layout.preferredHeight: 50
        }
    }

    // 通用规则编辑组件
    Component {
        id: ruleEditComponent

        Dialog {
            id: ruleEditDialog
            width: 600
            height: 700
            anchors.centerIn: parent
            modal: true

            // 组件属性
            property string dialogTitle: "规则编辑"
            property string ruleName: ""
            property string rulePattern: ""
            property int ruleDataType: 0
            property int ruleRiskLevel: 1
            property string ruleDescription: ""
            property bool ruleEnabled: true
            property string originalRuleName: ""
            property bool isEditMode: false

            // 信号
            signal ruleAccepted(string name, string pattern, int dataType, int riskLevel, bool enabled, string description, string originalName)
            signal ruleCancelled

            title: dialogTitle

            ScrollView {
                anchors.fill: parent
                contentWidth: availableWidth

                ColumnLayout {
                    width: parent.width
                    anchors.margins: 16
                    spacing: 12

                    // 规则名称
                    RowLayout {
                        Layout.fillWidth: true
                        Text {
                            text: "规则名称:"
                            Layout.preferredWidth: 80
                            font.bold: true
                        }
                        TextField {
                            id: ruleNameField
                            Layout.fillWidth: true
                            placeholderText: "请输入规则名称"
                            text: ruleEditDialog.ruleName
                        }
                    }

                    // 正则表达式
                    RowLayout {
                        Layout.fillWidth: true
                        Text {
                            text: "正则表达式:"
                            Layout.preferredWidth: 80
                            font.bold: true
                        }
                        TextField {
                            id: rulePatternField
                            Layout.fillWidth: true
                            placeholderText: "请输入正则表达式模式"
                            font.family: "Consolas"
                            text: ruleEditDialog.rulePattern
                        }
                    }

                    // 数据类型
                    RowLayout {
                        Layout.fillWidth: true
                        Text {
                            text: "数据类型:"
                            Layout.preferredWidth: 80
                            font.bold: true
                        }
                        ComboBox {
                            id: ruleDataTypeCombo
                            Layout.preferredWidth: 150
                            model: ["PII", "财务", "健康", "商业机密", "其他"]
                            currentIndex: ruleEditDialog.ruleDataType
                        }
                    }

                    // 风险级别
                    RowLayout {
                        Layout.fillWidth: true
                        Text {
                            text: "风险级别:"
                            Layout.preferredWidth: 80
                            font.bold: true
                        }
                        ComboBox {
                            id: ruleRiskLevelCombo
                            Layout.preferredWidth: 150
                            model: ["高风险", "中风险", "低风险", "无风险"]
                            currentIndex: ruleEditDialog.ruleRiskLevel
                        }
                    }

                    // 规则描述
                    RowLayout {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 100
                        Text {
                            text: "规则描述:"
                            Layout.preferredWidth: 80
                            Layout.alignment: Qt.AlignTop
                            font.bold: true
                        }
                        ScrollView {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            TextArea {
                                id: ruleDescriptionArea
                                placeholderText: "请输入规则描述（可选）"
                                wrapMode: TextArea.Wrap
                                text: ruleEditDialog.ruleDescription
                            }
                        }
                    }

                    // 规则测试
                    RowLayout {
                        Layout.fillWidth: true
                        Text {
                            text: "测试文本:"
                            Layout.preferredWidth: 80
                            font.bold: true
                        }
                        TextField {
                            id: ruleTestField
                            Layout.fillWidth: true
                            placeholderText: "输入测试文本验证正则表达式"
                        }
                        Button {
                            text: "测试"
                            enabled: rulePatternField.text.length > 0 && ruleTestField.text.length > 0
                            onClicked: {
                                try {
                                    var regex = new RegExp(rulePatternField.text);
                                    var match = regex.test(ruleTestField.text);
                                    testResultText.text = match ? "✓ 匹配成功" : "✗ 不匹配";
                                    testResultText.color = match ? Qt.color("#27ae60") : Qt.color("#e74c3c");
                                } catch (error) {
                                    testResultText.text = "✗ 正则表达式错误";
                                    testResultText.color = Qt.color("#e74c3c");
                                }
                            }
                        }
                    }

                    Text {
                        id: testResultText
                        text: ""
                        font.bold: true
                    }

                    // 启用状态
                    RowLayout {
                        Layout.fillWidth: true
                        Text {
                            text: "启用状态:"
                            Layout.preferredWidth: 80
                            font.bold: true
                        }
                        CheckBox {
                            id: ruleEnabledCheckBox
                            text: "启用此规则"
                            checked: ruleEditDialog.ruleEnabled
                        }
                    }

                    // 按钮区域
                    RowLayout {
                        Layout.fillWidth: true
                        Layout.topMargin: 16

                        Item {
                            Layout.fillWidth: true
                        }

                        Button {
                            text: "取消"
                            onClicked: {
                                ruleEditDialog.ruleCancelled();
                                ruleEditDialog.close();
                            }
                        }

                        Button {
                            text: ruleEditDialog.isEditMode ? "保存修改" : "创建规则"
                            Material.background: Qt.color("#27ae60")
                            Material.foreground: Qt.color("white")
                            enabled: ruleNameField.text.length > 0 && rulePatternField.text.length > 0
                            onClicked: {
                                ruleEditDialog.ruleAccepted(ruleNameField.text, rulePatternField.text, ruleDataTypeCombo.currentIndex, ruleRiskLevelCombo.currentIndex, ruleEnabledCheckBox.checked, ruleDescriptionArea.text, ruleEditDialog.originalRuleName);
                                ruleEditDialog.close();
                            }
                        }
                    }
                }
            }

            // 清空表单的函数
            function clearForm() {
                ruleNameField.text = "";
                rulePatternField.text = "";
                ruleDataTypeCombo.currentIndex = 0;
                ruleRiskLevelCombo.currentIndex = 1;
                ruleDescriptionArea.text = "";
                ruleEnabledCheckBox.checked = true;
                ruleTestField.text = "";
                testResultText.text = "";
            }

            // 加载规则数据的函数
            function loadRule(rule) {
                ruleName = rule.name || "";
                rulePattern = rule.pattern || "";
                ruleDataType = rule.dataType || 0;
                ruleRiskLevel = rule.riskLevel || 1;
                ruleDescription = rule.description || "";
                ruleEnabled = rule.enabled !== undefined ? rule.enabled : true;
                originalRuleName = rule.name || "";

                // 更新表单字段
                ruleNameField.text = ruleName;
                rulePatternField.text = rulePattern;
                ruleDataTypeCombo.currentIndex = ruleDataType;
                ruleRiskLevelCombo.currentIndex = ruleRiskLevel;
                ruleDescriptionArea.text = ruleDescription;
                ruleEnabledCheckBox.checked = ruleEnabled;
                ruleTestField.text = "";
                testResultText.text = "";
            }
        }
    }

    // 文件夹选择对话框
    FolderDialog {
        id: folderDialog
        title: "选择要扫描的目录"
        currentFolder: "file:///C:/"
        onAccepted: {
            scanPathField.text = selectedFolder.toString().replace("file:///", "");
        }
    }

    // 规则管理对话框
    Dialog {
        id: ruleManagementDialog
        title: "规则管理"
        width: 800
        height: 600
        anchors.centerIn: parent
        modal: true

        onOpened: {
            // 对话框打开时刷新规则列表
            managementRuleListView.model = dataDiscoveryEngine.getScanRules();
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 12

            // 工具栏
            RowLayout {
                Layout.fillWidth: true

                Text {
                    text: "规则列表"
                    font.pixelSize: 16
                    font.bold: true
                }

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "删除选中"
                    Material.background: Qt.color("#e74c3c")
                    Material.foreground: Qt.color("white")
                    enabled: managementRuleListView.currentIndex >= 0
                    onClicked: {
                        console.log("点击删除规则按钮");
                        if (managementRuleListView.currentIndex >= 0) {
                            var selectedRule = managementRuleListView.model[managementRuleListView.currentIndex];
                            var ruleNameToDelete = selectedRule.name;
                            console.log("准备删除规则:", ruleNameToDelete);

                            // 使用Qt.callLater避免在信号处理中直接调用可能导致死锁的函数
                            Qt.callLater(function () {
                                try {
                                    console.log("开始删除规则:", ruleNameToDelete);
                                    dataDiscoveryEngine.removeScanRule(ruleNameToDelete);
                                    // 延迟更新模型，确保信号处理完成
                                    Qt.callLater(function () {
                                        managementRuleListView.model = dataDiscoveryEngine.getScanRules();
                                    });
                                    console.log("规则删除完成");
                                } catch (error) {
                                    console.error("删除规则时发生错误:", error);
                                }
                            });
                        }
                    }
                }

                Button {
                    text: "编辑规则"
                    Material.background: Qt.color("#3498db")
                    Material.foreground: Qt.color("white")
                    enabled: managementRuleListView.currentIndex >= 0
                    onClicked: {
                        if (managementRuleListView.currentIndex >= 0) {
                            var selectedRule = managementRuleListView.model[managementRuleListView.currentIndex];

                            // 使用通用组件创建编辑规则对话框
                            var dialog = ruleEditComponent.createObject(root, {
                                "dialogTitle": "编辑扫描规则",
                                "isEditMode": true
                            });

                            // 连接信号
                            dialog.ruleAccepted.connect(function (name, pattern, dataType, riskLevel, enabled, description, originalName) {
                                console.log("编辑规则:", name);
                                Qt.callLater(function () {
                                    try {
                                        // 如果规则名称改变了，需要先删除旧规则
                                        if (originalName !== name) {
                                            dataDiscoveryEngine.removeScanRule(originalName);
                                        }

                                        dataDiscoveryEngine.addScanRule(name, pattern, dataType, riskLevel, enabled, description);

                                        // 延迟更新模型
                                        Qt.callLater(function () {
                                            managementRuleListView.model = dataDiscoveryEngine.getScanRules();
                                        });
                                        console.log("规则编辑完成");
                                    } catch (error) {
                                        console.error("编辑规则时发生错误:", error);
                                    }
                                });
                                dialog.destroy();
                            });

                            dialog.ruleCancelled.connect(function () {
                                dialog.destroy();
                            });

                            // 加载规则数据并打开对话框
                            dialog.loadRule(selectedRule);
                            dialog.open();
                        }
                    }
                }
            }

            // 规则列表
            Rectangle {
                Layout.fillWidth: true
                Layout.fillHeight: true
                border.color: Qt.color("#ddd")
                radius: 4

                ListView {
                    id: managementRuleListView
                    anchors.fill: parent
                    anchors.margins: 8
                    model: dataDiscoveryEngine.getScanRules()
                    currentIndex: -1
                    clip: true

                    delegate: Rectangle {
                        id: managementRuleDelegateItem
                        required property int index
                        required property var modelData
                        width: parent ? parent.width : 0
                        height: 60
                        color: managementRuleListView.currentIndex === managementRuleDelegateItem.index ? Qt.color("#e3f2fd") : (managementRuleDelegateItem.index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white"))
                        radius: 4

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                managementRuleListView.currentIndex = managementRuleDelegateItem.index;
                            }
                        }

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 8
                            spacing: 4

                            RowLayout {
                                Layout.fillWidth: true

                                Text {
                                    text: managementRuleDelegateItem.modelData.name
                                    font.bold: true
                                    font.pixelSize: 14
                                }

                                Item {
                                    Layout.fillWidth: true
                                }

                                Rectangle {
                                    Layout.preferredWidth: 60
                                    Layout.preferredHeight: 20
                                    color: managementRuleDelegateItem.modelData.enabled ? Qt.color("#27ae60") : Qt.color("#95a5a6")
                                    radius: 10

                                    Text {
                                        anchors.centerIn: parent
                                        text: managementRuleDelegateItem.modelData.enabled ? "启用" : "禁用"
                                        color: Qt.color("white")
                                        font.pixelSize: 10
                                    }
                                }

                                Rectangle {
                                    Layout.preferredWidth: 60
                                    Layout.preferredHeight: 20
                                    color: managementRuleDelegateItem.modelData.riskLevel === 0 ? Qt.color("#e74c3c") : managementRuleDelegateItem.modelData.riskLevel === 1 ? Qt.color("#f39c12") : Qt.color("#27ae60")
                                    radius: 10

                                    Text {
                                        anchors.centerIn: parent
                                        text: managementRuleDelegateItem.modelData.riskLevelName
                                        color: Qt.color("white")
                                        font.pixelSize: 10
                                    }
                                }
                            }

                            Text {
                                Layout.fillWidth: true
                                text: managementRuleDelegateItem.modelData.pattern
                                color: Qt.color("#7f8c8d")
                                font.family: "Consolas"
                                font.pixelSize: 12
                                elide: Text.ElideRight
                            }
                        }
                    }
                }
            }

            // 按钮区域
            RowLayout {
                Layout.fillWidth: true

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "关闭"
                    onClicked: {
                        ruleManagementDialog.close();
                    }
                }
            }
        }
    }
}
