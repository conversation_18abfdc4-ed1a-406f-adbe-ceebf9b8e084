#include "RiskAssessmentEngine.h"
#include "DataDiscoveryEngine.h"
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include <QStringConverter>

RiskAssessmentEngine::RiskAssessmentEngine(QObject *parent)
    : QObject(parent)
    , m_dataDiscoveryEngine(nullptr)
    , m_isAssessing(false)
    , m_refreshTimer(new QTimer(this))
{
    // qDebug() << "[RiskAssessmentEngine] 初始化风险评估引擎";

    // 设置刷新定时器（可选，用于定期更新）
    m_refreshTimer->setSingleShot(true);
    m_refreshTimer->setInterval(1000); // 1秒延迟，避免频繁更新

    connect(m_refreshTimer, &QTimer::timeout, this, &RiskAssessmentEngine::refreshAssessment);

    // 确保数据目录存在
    ensureDataDirectoryExists();

    // 加载历史评估结果
    loadAssessmentResults();

    // qDebug() << "[RiskAssessmentEngine] 风险评估引擎初始化完成";
}

RiskAssessmentEngine::~RiskAssessmentEngine()
{
    // qDebug() << "[RiskAssessmentEngine] 销毁风险评估引擎";

    // 保存当前评估结果
    if (!m_currentResult.assessmentId.isEmpty()) {
        saveAssessmentResults();
    }
}

void RiskAssessmentEngine::setDataDiscoveryEngine(DataDiscoveryEngine* engine)
{
    // qDebug() << "[RiskAssessmentEngine] 设置DataDiscoveryEngine引用";

    if (m_dataDiscoveryEngine) {
        // 断开之前的连接
        disconnect(m_dataDiscoveryEngine, nullptr, this, nullptr);
    }

    m_dataDiscoveryEngine = engine;

    if (m_dataDiscoveryEngine) {
        // 连接信号
        connect(m_dataDiscoveryEngine, &DataDiscoveryEngine::scanCompleted,
                this, &RiskAssessmentEngine::onDataDiscoveryScanCompleted);

        // qDebug() << "[RiskAssessmentEngine] DataDiscoveryEngine连接成功";

        // 如果已有扫描结果，立即进行评估
        if (!m_dataDiscoveryEngine->getScanResults().isEmpty()) {
            // qDebug() << "[RiskAssessmentEngine] 检测到现有扫描结果，开始风险评估";
            QTimer::singleShot(500, this, &RiskAssessmentEngine::startRiskAssessment);
        }
    }
}

void RiskAssessmentEngine::startRiskAssessment()
{
    // qDebug() << "[RiskAssessmentEngine] 开始风险评估";

    if (m_isAssessing) {
        // qDebug() << "[RiskAssessmentEngine] 风险评估正在进行中，跳过";
        return;
    }

    if (!m_dataDiscoveryEngine) {
        qWarning() << "[RiskAssessmentEngine] DataDiscoveryEngine未设置，无法进行风险评估";
        emit assessmentError("DataDiscoveryEngine未设置");
        return;
    }

    QVariantList scanResults = m_dataDiscoveryEngine->getScanResults();
    if (scanResults.isEmpty()) {
        qDebug() << "[RiskAssessmentEngine] 没有扫描结果，无法进行风险评估";
        emit assessmentError("没有可用的扫描结果");
        return;
    }

    m_isAssessing = true;
    emit isAssessingChanged();

    // qDebug() << "[RiskAssessmentEngine] 开始执行风险评估，扫描结果数量:" << scanResults.size();

    // 异步执行评估以避免阻塞UI
    QTimer::singleShot(0, this, &RiskAssessmentEngine::performRiskAssessment);
}

void RiskAssessmentEngine::refreshAssessment()
{
    // qDebug() << "[RiskAssessmentEngine] 刷新风险评估";
    startRiskAssessment();
}

void RiskAssessmentEngine::performRiskAssessment()
{
    // qDebug() << "[RiskAssessmentEngine] 执行风险评估计算";

    try {
        // 重置当前结果
        m_currentResult = RiskAssessmentResult();
        m_currentResult.assessmentId = QString("RISK_%1").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss"));
        m_currentResult.assessmentTime = QDateTime::currentDateTime();

        // qDebug() << "[RiskAssessmentEngine] 评估ID:" << m_currentResult.assessmentId;

        // 获取扫描结果
        QVariantList scanResults = m_dataDiscoveryEngine->getScanResults();
        QVariantMap scanStats = m_dataDiscoveryEngine->scanStatistics();

        // qDebug() << "[RiskAssessmentEngine] 扫描结果数量:" << scanResults.size();
        // qDebug() << "[RiskAssessmentEngine] 扫描统计:" << scanStats;

        // 设置基本统计信息
        m_currentResult.totalSensitiveItems = scanResults.size();
        m_currentResult.totalSensitiveFiles = scanStats.value("totalFilesScanned", 0).toInt();

        // 执行各项风险分析
        analyzeDataTypeRisks();
        analyzeFileRisks();
        analyzeDirectoryRisks();
        calculateTotalRiskScore();
        identifyCriticalIssues();

        // 添加到历史记录
        addToHistory();

        // qDebug() << "[RiskAssessmentEngine] 风险评估完成";
        // qDebug() << "[RiskAssessmentEngine] 总风险评分:" << m_currentResult.totalRiskScore;
        // qDebug() << "[RiskAssessmentEngine] 风险等级:" << m_currentResult.riskLevel;

        m_isAssessing = false;
        emit isAssessingChanged();
        emit currentAssessmentChanged();
        emit totalRiskScoreChanged();
        emit riskLevelChanged();
        emit assessmentCompleted();

        // 保存评估结果
        saveAssessmentResults();

    } catch (const std::exception& e) {
        qCritical() << "[RiskAssessmentEngine] 风险评估过程中发生异常:" << e.what();
        m_isAssessing = false;
        emit isAssessingChanged();
        emit assessmentError(QString("评估过程中发生错误: %1").arg(e.what()));
    }
}

void RiskAssessmentEngine::calculateTotalRiskScore()
{
    qDebug() << "[RiskAssessmentEngine] 计算总风险评分";

    if (!m_dataDiscoveryEngine) {
        return;
    }

    QVariantList scanResults = m_dataDiscoveryEngine->getScanResults();
    if (scanResults.isEmpty()) {
        m_currentResult.totalRiskScore = 0.0;
        m_currentResult.riskLevel = "无风险";
        return;
    }

    double totalScore = 0.0;
    int highRiskCount = 0;
    int mediumRiskCount = 0;
    int lowRiskCount = 0;

    // 计算基于风险等级的评分
    for (const QVariant& resultVar : scanResults) {
        QVariantMap result = resultVar.toMap();

        // 优先使用riskLevelName字段，如果不存在则使用riskLevel数字字段
        QString riskLevelName = result.value("riskLevelName", "").toString();
        if (riskLevelName.isEmpty()) {
            // 如果没有riskLevelName，则从数字riskLevel转换
            int riskLevelInt = result.value("riskLevel", -1).toInt();
            riskLevelName = convertIntToRiskLevelString(riskLevelInt);
        }

        // qDebug() << "[RiskAssessmentEngine] 处理风险项，风险等级:" << riskLevelName;

        if (riskLevelName == "高风险" || riskLevelName == "High") {
            totalScore += 10.0;  // 高风险项权重
            highRiskCount++;
        } else if (riskLevelName == "中风险" || riskLevelName == "Medium") {
            totalScore += 5.0;   // 中风险项权重
            mediumRiskCount++;
        } else if (riskLevelName == "低风险" || riskLevelName == "Low") {
            totalScore += 2.0;   // 低风险项权重
            lowRiskCount++;
        }
    }

    // 归一化评分到0-100范围
    int totalItems = scanResults.size();
    if (totalItems > 0) {
        // 基础评分：基于风险项数量和权重
        double baseScore = (totalScore / totalItems) * 10.0;

        // 调整因子：考虑高风险项比例
        double highRiskRatio = (double)highRiskCount / totalItems;
        double adjustmentFactor = 1.0 + (highRiskRatio * 2.0); // 高风险项比例越高，调整因子越大

        m_currentResult.totalRiskScore = qMin(100.0, baseScore * adjustmentFactor);
    } else {
        m_currentResult.totalRiskScore = 0.0;
    }

    // 计算平均每文件风险值
    if (m_currentResult.totalSensitiveFiles > 0) {
        m_currentResult.averageRiskPerFile = m_currentResult.totalRiskScore / m_currentResult.totalSensitiveFiles;
    }

    // 确定风险等级
    m_currentResult.riskLevel = determineRiskLevel(m_currentResult.totalRiskScore);

    // 保存风险等级统计
    QVariantMap riskCounts;
    riskCounts["high"] = highRiskCount;
    riskCounts["medium"] = mediumRiskCount;
    riskCounts["low"] = lowRiskCount;
    riskCounts["total"] = totalItems;
    m_currentResult.riskLevelCounts = riskCounts;

    qDebug() << "[RiskAssessmentEngine] 风险评分计算完成:";
    qDebug() << "  - 总评分:" << m_currentResult.totalRiskScore;
    qDebug() << "  - 风险等级:" << m_currentResult.riskLevel;
    qDebug() << "  - 高风险项:" << highRiskCount;
    qDebug() << "  - 中风险项:" << mediumRiskCount;
    qDebug() << "  - 低风险项:" << lowRiskCount;
}

void RiskAssessmentEngine::analyzeDataTypeRisks()
{
    // qDebug() << "[RiskAssessmentEngine] 分析数据类型风险";

    if (!m_dataDiscoveryEngine) {
        return;
    }

    QVariantList scanResults = m_dataDiscoveryEngine->getScanResults();
    QVariantMap dataTypeRisks;

    // 按数据类型分组统计
    QMap<QString, QVariantList> dataTypeGroups;
    for (const QVariant& resultVar : scanResults) {
        QVariantMap result = resultVar.toMap();
        QString dataType = result.value("dataType", "Other").toString();
        dataTypeGroups[dataType].append(result);
    }

    // 计算每种数据类型的风险
    for (auto it = dataTypeGroups.begin(); it != dataTypeGroups.end(); ++it) {
        QString dataType = it.key();
        QVariantList items = it.value();

        double risk = calculateDataTypeRisk(dataType, items);

        QVariantMap typeRisk;
        typeRisk["count"] = items.size();
        typeRisk["riskScore"] = risk;
        typeRisk["riskLevel"] = determineRiskLevel(risk);

        dataTypeRisks[dataType] = typeRisk;

        // qDebug() << "[RiskAssessmentEngine] 数据类型" << dataType << "风险:" << risk << "项目数:" << items.size();
    }

    m_currentResult.dataTypeRisks = dataTypeRisks;
}

void RiskAssessmentEngine::analyzeFileRisks()
{
    // qDebug() << "[RiskAssessmentEngine] 分析文件风险";

    if (!m_dataDiscoveryEngine) {
        return;
    }

    QVariantList scanResults = m_dataDiscoveryEngine->getScanResults();
    QVariantMap fileRisks;

    // 按文件路径分组统计
    QMap<QString, QVariantList> fileGroups;
    for (const QVariant& resultVar : scanResults) {
        QVariantMap result = resultVar.toMap();
        QString filePath = result.value("filePath", "").toString();
        fileGroups[filePath].append(result);
    }

    // 计算每个文件的风险（只保存前20个最高风险的文件）
    QList<QPair<double, QString>> fileRiskList;

    for (auto it = fileGroups.begin(); it != fileGroups.end(); ++it) {
        QString filePath = it.key();
        QVariantList items = it.value();

        double risk = calculateFileRisk(filePath, items);
        fileRiskList.append(qMakePair(risk, filePath));
    }

    // 按风险评分排序
    std::sort(fileRiskList.begin(), fileRiskList.end(),
              [](const QPair<double, QString>& a, const QPair<double, QString>& b) {
                  return a.first > b.first;
              });

    // 保存前20个最高风险文件
    int maxFiles = qMin(20, fileRiskList.size());
    for (int i = 0; i < maxFiles; ++i) {
        double risk = fileRiskList[i].first;
        QString filePath = fileRiskList[i].second;
        QVariantList items = fileGroups[filePath];

        QVariantMap fileRisk;
        fileRisk["sensitiveItemCount"] = items.size();
        fileRisk["riskScore"] = risk;
        fileRisk["riskLevel"] = determineRiskLevel(risk);

        fileRisks[filePath] = fileRisk;
    }

    m_currentResult.fileRisks = fileRisks;

    // qDebug() << "[RiskAssessmentEngine] 文件风险分析完成，分析了" << fileGroups.size() << "个文件，保存了前" << maxFiles << "个高风险文件";
}

void RiskAssessmentEngine::analyzeDirectoryRisks()
{
    // qDebug() << "[RiskAssessmentEngine] 分析目录风险";

    if (!m_dataDiscoveryEngine) {
        return;
    }

    QVariantList scanResults = m_dataDiscoveryEngine->getScanResults();
    QVariantMap directoryRisks;

    // 按目录路径分组统计
    QMap<QString, QVariantList> dirGroups;
    for (const QVariant& resultVar : scanResults) {
        QVariantMap result = resultVar.toMap();
        QString filePath = result.value("filePath", "").toString();
        QString dirPath = QFileInfo(filePath).absolutePath();
        dirGroups[dirPath].append(result);
    }

    // 计算每个目录的风险（只保存前10个最高风险的目录）
    QList<QPair<double, QString>> dirRiskList;

    for (auto it = dirGroups.begin(); it != dirGroups.end(); ++it) {
        QString dirPath = it.key();
        QVariantList items = it.value();

        double risk = calculateDirectoryRisk(dirPath, items);
        dirRiskList.append(qMakePair(risk, dirPath));
    }

    // 按风险评分排序
    std::sort(dirRiskList.begin(), dirRiskList.end(),
              [](const QPair<double, QString>& a, const QPair<double, QString>& b) {
                  return a.first > b.first;
              });

    // 保存前10个最高风险目录
    int maxDirs = qMin(10, dirRiskList.size());
    for (int i = 0; i < maxDirs; ++i) {
        double risk = dirRiskList[i].first;
        QString dirPath = dirRiskList[i].second;
        QVariantList items = dirGroups[dirPath];

        QVariantMap dirRisk;
        dirRisk["sensitiveItemCount"] = items.size();
        dirRisk["riskScore"] = risk;
        dirRisk["riskLevel"] = determineRiskLevel(risk);

        directoryRisks[dirPath] = dirRisk;
    }

    m_currentResult.directoryRisks = directoryRisks;

    // qDebug() << "[RiskAssessmentEngine] 目录风险分析完成，分析了" << dirGroups.size() << "个目录，保存了前" << maxDirs << "个高风险目录";
}

void RiskAssessmentEngine::identifyCriticalIssues()
{
    // qDebug() << "[RiskAssessmentEngine] 识别关键问题";

    QStringList criticalIssues;

    // 检查高风险数据类型
    QVariantMap dataTypeRisks = m_currentResult.dataTypeRisks;
    for (auto it = dataTypeRisks.begin(); it != dataTypeRisks.end(); ++it) {
        QString dataType = it.key();
        QVariantMap typeRisk = it.value().toMap();
        double riskScore = typeRisk.value("riskScore", 0.0).toDouble();
        int count = typeRisk.value("count", 0).toInt();

        if (riskScore >= HIGH_RISK_THRESHOLD && count > 10) {
            criticalIssues << QString("发现%1个%2类型的高风险敏感数据").arg(count).arg(dataType);
        }
    }

    // 检查高风险文件
    QVariantMap fileRisks = m_currentResult.fileRisks;
    int highRiskFileCount = 0;
    for (auto it = fileRisks.begin(); it != fileRisks.end(); ++it) {
        QVariantMap fileRisk = it.value().toMap();
        double riskScore = fileRisk.value("riskScore", 0.0).toDouble();
        if (riskScore >= HIGH_RISK_THRESHOLD) {
            highRiskFileCount++;
        }
    }

    if (highRiskFileCount > 5) {
        criticalIssues << QString("发现%1个高风险文件需要立即处理").arg(highRiskFileCount);
    }

    // 检查总体风险评分
    if (m_currentResult.totalRiskScore >= HIGH_RISK_THRESHOLD) {
        criticalIssues << "系统整体风险评分过高，需要立即采取缓解措施";
    }

    // 检查敏感数据项数量
    if (m_currentResult.totalSensitiveItems > 500) {
        criticalIssues << QString("检测到%1个敏感数据项，数量过多").arg(m_currentResult.totalSensitiveItems);
    }

    m_currentResult.criticalIssues = criticalIssues;

    // qDebug() << "[RiskAssessmentEngine] 识别了" << criticalIssues.size() << "个关键问题";
}

// 辅助计算方法
double RiskAssessmentEngine::calculateDataTypeRisk(const QString& dataType, const QVariantList& items)
{
    if (items.isEmpty()) {
        return 0.0;
    }

    double totalRisk = 0.0;

    // 数据类型基础风险权重
    double baseWeight = 1.0;
    if (dataType == "PII") {
        baseWeight = 3.0;
    } else if (dataType == "Financial") {
        baseWeight = 4.0;
    } else if (dataType == "Health") {
        baseWeight = 4.5;
    } else if (dataType == "BusinessSecret") {
        baseWeight = 3.5;
    }

    // 计算风险项权重总和
    for (const QVariant& itemVar : items) {
        QVariantMap item = itemVar.toMap();

        // 优先使用riskLevelName字段，如果不存在则使用riskLevel数字字段
        QString riskLevelName = item.value("riskLevelName", "").toString();
        if (riskLevelName.isEmpty()) {
            int riskLevelInt = item.value("riskLevel", -1).toInt();
            riskLevelName = convertIntToRiskLevelString(riskLevelInt);
        }

        if (riskLevelName == "高风险" || riskLevelName == "High") {
            totalRisk += 10.0 * baseWeight;
        } else if (riskLevelName == "中风险" || riskLevelName == "Medium") {
            totalRisk += 5.0 * baseWeight;
        } else if (riskLevelName == "低风险" || riskLevelName == "Low") {
            totalRisk += 2.0 * baseWeight;
        }
    }

    // 归一化到0-100范围
    double normalizedRisk = (totalRisk / items.size()) * 2.0;
    return qMin(100.0, normalizedRisk);
}

double RiskAssessmentEngine::calculateFileRisk(const QString& filePath, const QVariantList& items)
{
    if (items.isEmpty()) {
        return 0.0;
    }

    double totalRisk = 0.0;

    // 文件扩展名风险权重
    QFileInfo fileInfo(filePath);
    QString extension = fileInfo.suffix().toLower();
    double extensionWeight = 1.0;

    if (extension == "db" || extension == "sql" || extension == "mdb") {
        extensionWeight = 2.0;  // 数据库文件
    } else if (extension == "xlsx" || extension == "csv" || extension == "xls") {
        extensionWeight = 1.5;  // 电子表格文件
    } else if (extension == "txt" || extension == "log") {
        extensionWeight = 1.2;  // 文本文件
    }

    // 计算风险项权重总和
    for (const QVariant& itemVar : items) {
        QVariantMap item = itemVar.toMap();

        // 优先使用riskLevelName字段，如果不存在则使用riskLevel数字字段
        QString riskLevelName = item.value("riskLevelName", "").toString();
        if (riskLevelName.isEmpty()) {
            int riskLevelInt = item.value("riskLevel", -1).toInt();
            riskLevelName = convertIntToRiskLevelString(riskLevelInt);
        }

        if (riskLevelName == "高风险" || riskLevelName == "High") {
            totalRisk += 10.0 * extensionWeight;
        } else if (riskLevelName == "中风险" || riskLevelName == "Medium") {
            totalRisk += 5.0 * extensionWeight;
        } else if (riskLevelName == "低风险" || riskLevelName == "Low") {
            totalRisk += 2.0 * extensionWeight;
        }
    }

    // 考虑敏感项密度
    double densityFactor = qMin(2.0, items.size() / 10.0);

    // 归一化到0-100范围
    double normalizedRisk = (totalRisk / items.size()) * densityFactor * 3.0;
    return qMin(100.0, normalizedRisk);
}

double RiskAssessmentEngine::calculateDirectoryRisk(const QString& dirPath, const QVariantList& items)
{
    if (items.isEmpty()) {
        return 0.0;
    }

    double totalRisk = 0.0;

    // 目录路径风险权重
    double pathWeight = 1.0;
    QString lowerPath = dirPath.toLower();

    if (lowerPath.contains("database") || lowerPath.contains("db")) {
        pathWeight = 2.0;
    } else if (lowerPath.contains("backup") || lowerPath.contains("bak")) {
        pathWeight = 1.8;
    } else if (lowerPath.contains("config") || lowerPath.contains("conf")) {
        pathWeight = 1.5;
    } else if (lowerPath.contains("temp") || lowerPath.contains("tmp")) {
        pathWeight = 1.3;
    }

    // 计算风险项权重总和
    for (const QVariant& itemVar : items) {
        QVariantMap item = itemVar.toMap();

        // 优先使用riskLevelName字段，如果不存在则使用riskLevel数字字段
        QString riskLevelName = item.value("riskLevelName", "").toString();
        if (riskLevelName.isEmpty()) {
            int riskLevelInt = item.value("riskLevel", -1).toInt();
            riskLevelName = convertIntToRiskLevelString(riskLevelInt);
        }

        if (riskLevelName == "高风险" || riskLevelName == "High") {
            totalRisk += 10.0 * pathWeight;
        } else if (riskLevelName == "中风险" || riskLevelName == "Medium") {
            totalRisk += 5.0 * pathWeight;
        } else if (riskLevelName == "低风险" || riskLevelName == "Low") {
            totalRisk += 2.0 * pathWeight;
        }
    }

    // 考虑敏感项密度
    double densityFactor = qMin(3.0, items.size() / 20.0);

    // 归一化到0-100范围
    double normalizedRisk = (totalRisk / items.size()) * densityFactor * 2.0;
    return qMin(100.0, normalizedRisk);
}

QString RiskAssessmentEngine::determineRiskLevel(double score)
{
    if (score >= HIGH_RISK_THRESHOLD) {
        return "高风险";
    } else if (score >= MEDIUM_RISK_THRESHOLD) {
        return "中风险";
    } else if (score >= LOW_RISK_THRESHOLD) {
        return "低风险";
    } else {
        return "无风险";
    }
}

QString RiskAssessmentEngine::convertIntToRiskLevelString(int riskLevel) const
{
    switch (riskLevel) {
        case 0:
            return "高风险";
        case 1:
            return "中风险";
        case 2:
            return "低风险";
        case 3:
            return "无风险";
        default:
            return "未知风险";
    }
}

// 历史管理方法
void RiskAssessmentEngine::addToHistory()
{
    // qDebug() << "[RiskAssessmentEngine] 添加评估结果到历史记录";

    QMutexLocker locker(&m_historyMutex);

    RiskAssessmentHistory historyItem(
        m_currentResult.assessmentId,
        m_currentResult.assessmentTime,
        m_currentResult.totalRiskScore,
        m_currentResult.riskLevel
    );

    m_assessmentHistory.prepend(historyItem);

    // 维护历史记录数量限制
    maintainHistoryLimit();

    emit assessmentHistoryChanged();

    // qDebug() << "[RiskAssessmentEngine] 历史记录已更新，当前记录数:" << m_assessmentHistory.size();
}

void RiskAssessmentEngine::maintainHistoryLimit()
{
    while (m_assessmentHistory.size() > MAX_HISTORY_COUNT) {
        m_assessmentHistory.removeLast();
    }
}

// 文件操作方法
QString RiskAssessmentEngine::getAssessmentResultsFilePath() const
{
    QString appDir = QCoreApplication::applicationDirPath();
    QString dataDir = appDir + "/src/data/";
    return dataDir + "riskAssessmentResults.json";
}

QString RiskAssessmentEngine::getAssessmentHistoryFilePath() const
{
    QString appDir = QCoreApplication::applicationDirPath();
    QString dataDir = appDir + "/src/data/";
    return dataDir + "riskAssessmentHistory.json";
}

bool RiskAssessmentEngine::ensureDataDirectoryExists() const
{
    QString appDir = QCoreApplication::applicationDirPath();
    QString dataDir = appDir + "/src/data/";

    QDir dir;
    if (!dir.exists(dataDir)) {
        bool created = dir.mkpath(dataDir);
        if (created) {
            // qDebug() << "[RiskAssessmentEngine] 创建数据目录:" << dataDir;
        } else {
            qWarning() << "[RiskAssessmentEngine] 无法创建数据目录:" << dataDir;
        }
        return created;
    }
    return true;
}

bool RiskAssessmentEngine::saveAssessmentResults()
{
    // qDebug() << "[RiskAssessmentEngine] 保存评估结果";

    if (!ensureDataDirectoryExists()) {
        qWarning() << "[RiskAssessmentEngine] 无法创建数据目录";
        return false;
    }

    // 保存当前详细评估结果
    QString resultsFilePath = getAssessmentResultsFilePath();
    QFile resultsFile(resultsFilePath);

    if (!resultsFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "[RiskAssessmentEngine] 无法打开评估结果文件进行写入:" << resultsFile.errorString();
        return false;
    }

    QJsonObject resultObj = QJsonObject::fromVariantMap(resultToVariant(m_currentResult));
    QJsonDocument resultDoc(resultObj);

    resultsFile.write(resultDoc.toJson());
    resultsFile.close();

    // qDebug() << "[RiskAssessmentEngine] 评估结果已保存到:" << resultsFilePath;

    // 保存历史记录
    QString historyFilePath = getAssessmentHistoryFilePath();
    QFile historyFile(historyFilePath);

    if (!historyFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "[RiskAssessmentEngine] 无法打开历史记录文件进行写入:" << historyFile.errorString();
        return false;
    }

    QJsonArray historyArray;
    QMutexLocker locker(&m_historyMutex);
    for (const RiskAssessmentHistory& history : m_assessmentHistory) {
        QJsonObject historyObj = QJsonObject::fromVariantMap(historyToVariant(history));
        historyArray.append(historyObj);
    }

    QJsonDocument historyDoc(historyArray);
    historyFile.write(historyDoc.toJson());
    historyFile.close();

    // qDebug() << "[RiskAssessmentEngine] 历史记录已保存到:" << historyFilePath;

    return true;
}

bool RiskAssessmentEngine::loadAssessmentResults()
{
    // qDebug() << "[RiskAssessmentEngine] 加载评估结果";

    // 加载当前详细评估结果
    QString resultsFilePath = getAssessmentResultsFilePath();
    QFile resultsFile(resultsFilePath);

    if (resultsFile.exists() && resultsFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QByteArray data = resultsFile.readAll();
        resultsFile.close();

        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(data, &error);

        if (error.error == QJsonParseError::NoError && doc.isObject()) {
            QVariantMap resultMap = doc.object().toVariantMap();
            m_currentResult = variantToResult(resultMap);

            // qDebug() << "[RiskAssessmentEngine] 评估结果加载成功，评估ID:" << m_currentResult.assessmentId;
            emit currentAssessmentChanged();
            emit totalRiskScoreChanged();
            emit riskLevelChanged();
        } else {
            qWarning() << "[RiskAssessmentEngine] 评估结果文件解析失败:" << error.errorString();
        }
    } else {
        qDebug() << "[RiskAssessmentEngine] 评估结果文件不存在或无法读取:" << resultsFilePath;
    }

    // 加载历史记录
    QString historyFilePath = getAssessmentHistoryFilePath();
    QFile historyFile(historyFilePath);

    if (historyFile.exists() && historyFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QByteArray data = historyFile.readAll();
        historyFile.close();

        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(data, &error);

        if (error.error == QJsonParseError::NoError && doc.isArray()) {
            QJsonArray historyArray = doc.array();

            QMutexLocker locker(&m_historyMutex);
            m_assessmentHistory.clear();

            for (const QJsonValue& value : historyArray) {
                if (value.isObject()) {
                    QVariantMap historyMap = value.toObject().toVariantMap();
                    RiskAssessmentHistory history = variantToHistory(historyMap);
                    m_assessmentHistory.append(history);
                }
            }

            // qDebug() << "[RiskAssessmentEngine] 历史记录加载成功，记录数:" << m_assessmentHistory.size();
            emit assessmentHistoryChanged();
        } else {
            qWarning() << "[RiskAssessmentEngine] 历史记录文件解析失败:" << error.errorString();
        }
    } else {
        qDebug() << "[RiskAssessmentEngine] 历史记录文件不存在或无法读取:" << historyFilePath;
    }

    return true;
}

// 信号处理方法
void RiskAssessmentEngine::onDataDiscoveryScanCompleted()
{
    // qDebug() << "[RiskAssessmentEngine] 收到数据发现扫描完成信号，开始风险评估";

    // 延迟一点时间开始评估，确保扫描结果已完全处理
    m_refreshTimer->start();
}

void RiskAssessmentEngine::onDataDiscoveryResultsChanged()
{
    qDebug() << "[RiskAssessmentEngine] 收到数据发现结果变更信号";

    // 如果当前没有在评估，则启动评估
    if (!m_isAssessing) {
        m_refreshTimer->start();
    }
}

// 公共接口方法
QVariantMap RiskAssessmentEngine::currentAssessment() const
{
    QMutexLocker locker(&m_resultMutex);
    return resultToVariant(m_currentResult);
}

QVariantList RiskAssessmentEngine::assessmentHistory() const
{
    QMutexLocker locker(&m_historyMutex);
    QVariantList historyList;

    for (const RiskAssessmentHistory& history : m_assessmentHistory) {
        historyList.append(historyToVariant(history));
    }

    return historyList;
}

QVariantMap RiskAssessmentEngine::getDetailedAssessment() const
{
    return currentAssessment();
}

QVariantList RiskAssessmentEngine::getRiskTrends() const
{
    QMutexLocker locker(&m_historyMutex);
    QVariantList trends;

    for (const RiskAssessmentHistory& history : m_assessmentHistory) {
        QVariantMap trend;
        trend["time"] = history.assessmentTime;
        trend["score"] = history.totalRiskScore;
        trend["level"] = history.riskLevel;
        trends.append(trend);
    }

    return trends;
}

QStringList RiskAssessmentEngine::getRecommendations() const
{
    QMutexLocker locker(&m_resultMutex);
    return m_currentResult.recommendations;
}

QStringList RiskAssessmentEngine::getCriticalIssues() const
{
    QMutexLocker locker(&m_resultMutex);
    return m_currentResult.criticalIssues;
}

void RiskAssessmentEngine::clearAssessmentHistory()
{
    qDebug() << "[RiskAssessmentEngine] 清除评估历史记录";

    QMutexLocker locker(&m_historyMutex);
    m_assessmentHistory.clear();
    emit assessmentHistoryChanged();

    // 删除历史记录文件
    QString historyFilePath = getAssessmentHistoryFilePath();
    QFile::remove(historyFilePath);
}

// 数据转换方法
QVariantMap RiskAssessmentEngine::resultToVariant(const RiskAssessmentResult& result) const
{
    QVariantMap map;
    map["assessmentId"] = result.assessmentId;
    map["assessmentTime"] = result.assessmentTime;
    map["version"] = result.version;
    map["totalRiskScore"] = result.totalRiskScore;
    map["riskLevel"] = result.riskLevel;
    map["dataTypeRisks"] = result.dataTypeRisks;
    map["riskLevelCounts"] = result.riskLevelCounts;
    map["fileRisks"] = result.fileRisks;
    map["directoryRisks"] = result.directoryRisks;
    map["riskTrends"] = result.riskTrends;
    map["recommendations"] = QVariantList(result.recommendations.begin(), result.recommendations.end());
    map["criticalIssues"] = QVariantList(result.criticalIssues.begin(), result.criticalIssues.end());
    map["totalSensitiveFiles"] = result.totalSensitiveFiles;
    map["totalSensitiveItems"] = result.totalSensitiveItems;
    map["averageRiskPerFile"] = result.averageRiskPerFile;

    // 添加基于规则的风险评分相关信息
    QVariantMap ruleBasedInfo;
    ruleBasedInfo["lastCalculationTime"] = QDateTime::currentDateTime();
    ruleBasedInfo["calculationMethod"] = "基于文件路径和内容的规则评分";
    ruleBasedInfo["supportedDataTypes"] = QStringList{"所有数据资产", "PII数据", "财务数据", "健康数据", "商业机密"};
    map["ruleBasedRiskInfo"] = ruleBasedInfo;
    return map;
}

RiskAssessmentResult RiskAssessmentEngine::variantToResult(const QVariantMap& variant) const
{
    RiskAssessmentResult result;
    result.assessmentId = variant.value("assessmentId", "").toString();
    result.assessmentTime = variant.value("assessmentTime", QDateTime::currentDateTime()).toDateTime();
    result.version = variant.value("version", "1.0").toString();
    result.totalRiskScore = variant.value("totalRiskScore", 0.0).toDouble();
    result.riskLevel = variant.value("riskLevel", "无风险").toString();
    result.dataTypeRisks = variant.value("dataTypeRisks", QVariantMap()).toMap();
    result.riskLevelCounts = variant.value("riskLevelCounts", QVariantMap()).toMap();
    result.fileRisks = variant.value("fileRisks", QVariantMap()).toMap();
    result.directoryRisks = variant.value("directoryRisks", QVariantMap()).toMap();
    result.riskTrends = variant.value("riskTrends", QVariantList()).toList();

    QVariantList recList = variant.value("recommendations", QVariantList()).toList();
    for (const QVariant& rec : recList) {
        result.recommendations.append(rec.toString());
    }

    QVariantList issueList = variant.value("criticalIssues", QVariantList()).toList();
    for (const QVariant& issue : issueList) {
        result.criticalIssues.append(issue.toString());
    }

    result.totalSensitiveFiles = variant.value("totalSensitiveFiles", 0).toInt();
    result.totalSensitiveItems = variant.value("totalSensitiveItems", 0).toInt();
    result.averageRiskPerFile = variant.value("averageRiskPerFile", 0.0).toDouble();

    return result;
}

QVariantMap RiskAssessmentEngine::historyToVariant(const RiskAssessmentHistory& history) const
{
    QVariantMap map;
    map["assessmentId"] = history.assessmentId;
    map["assessmentTime"] = history.assessmentTime;
    map["totalRiskScore"] = history.totalRiskScore;
    map["riskLevel"] = history.riskLevel;
    return map;
}

RiskAssessmentHistory RiskAssessmentEngine::variantToHistory(const QVariantMap& variant) const
{
    RiskAssessmentHistory history;
    history.assessmentId = variant.value("assessmentId", "").toString();
    history.assessmentTime = variant.value("assessmentTime", QDateTime::currentDateTime()).toDateTime();
    history.totalRiskScore = variant.value("totalRiskScore", 0.0).toDouble();
    history.riskLevel = variant.value("riskLevel", "无风险").toString();
    return history;
}

// 基于规则的风险评分功能实现
QVariantList RiskAssessmentEngine::getFilteredRiskData(const QString& dataType) const
{
    // qDebug() << "[RiskAssessmentEngine] 获取过滤的风险数据，数据类型:" << dataType;

    QMutexLocker locker(&m_resultMutex);
    QVariantList filteredData;

    if (m_currentResult.fileRisks.isEmpty()) {
        qDebug() << "[RiskAssessmentEngine] 没有文件风险数据";
        return filteredData;
    }

    QVariantMap fileRisks = m_currentResult.fileRisks;

    for (auto it = fileRisks.begin(); it != fileRisks.end(); ++it) {
        QString filePath = it.key();
        QVariantMap fileRisk = it.value().toMap();

        bool shouldInclude = false;
        QString detectedType = detectDataTypeFromPath(filePath);

        if (dataType == "所有数据资产") {
            shouldInclude = true;
        } else if (dataType == "PII数据" && (detectedType == "PII" || filePath.toLower().contains("personal") || filePath.toLower().contains("customer"))) {
            shouldInclude = true;
        } else if (dataType == "财务数据" && (detectedType == "Financial" || filePath.toLower().contains("financial") || filePath.toLower().contains("finance"))) {
            shouldInclude = true;
        } else if (dataType == "健康数据" && (detectedType == "Health" || filePath.toLower().contains("health") || filePath.toLower().contains("medical"))) {
            shouldInclude = true;
        } else if (dataType == "商业机密" && (detectedType == "BusinessSecret" || filePath.toLower().contains("secret") || filePath.toLower().contains("confidential"))) {
            shouldInclude = true;
        }

        if (shouldInclude) {
            QFileInfo fileInfo(filePath);
            QString fileName = fileInfo.fileName();

            QVariantMap item;
            item["name"] = fileName;
            item["type"] = detectedType;
            item["score"] = qRound(fileRisk.value("riskScore", 0.0).toDouble());
            item["level"] = fileRisk.value("riskLevel", "未知").toString();
            item["filePath"] = filePath;
            item["sensitiveItemCount"] = fileRisk.value("sensitiveItemCount", 0).toInt();

            filteredData.append(item);
        }
    }

    // 按风险评分排序
    std::sort(filteredData.begin(), filteredData.end(), [](const QVariant& a, const QVariant& b) {
        return a.toMap().value("score", 0).toInt() > b.toMap().value("score", 0).toInt();
    });

    // 限制返回数量
    if (filteredData.size() > 20) {
        filteredData = filteredData.mid(0, 20);
    }

    // qDebug() << "[RiskAssessmentEngine] 过滤后的数据数量:" << filteredData.size();
    return filteredData;
}

QString RiskAssessmentEngine::detectDataTypeFromPath(const QString& filePath) const
{
    QString lowerPath = filePath.toLower();

    if (lowerPath.contains("personal") || lowerPath.contains("customer") || lowerPath.contains("user") || lowerPath.contains("employee")) {
        return "PII";
    } else if (lowerPath.contains("financial") || lowerPath.contains("finance") || lowerPath.contains("payment") || lowerPath.contains("invoice")) {
        return "财务";
    } else if (lowerPath.contains("health") || lowerPath.contains("medical") || lowerPath.contains("patient")) {
        return "健康";
    } else if (lowerPath.contains("secret") || lowerPath.contains("confidential") || lowerPath.contains("proprietary")) {
        return "商业机密";
    } else {
        return "其他";
    }
}

QVariantMap RiskAssessmentEngine::getRiskDetailsForFile(const QString& filePath) const
{
    // qDebug() << "[RiskAssessmentEngine] 获取文件风险详情:" << filePath;

    QMutexLocker locker(&m_resultMutex);
    QVariantMap details;

    if (m_currentResult.fileRisks.contains(filePath)) {
        QVariantMap fileRisk = m_currentResult.fileRisks.value(filePath).toMap();

        details["filePath"] = filePath;
        details["riskScore"] = fileRisk.value("riskScore", 0.0);
        details["riskLevel"] = fileRisk.value("riskLevel", "未知");
        details["sensitiveItemCount"] = fileRisk.value("sensitiveItemCount", 0);
        details["dataType"] = detectDataTypeFromPath(filePath);

        // 添加文件基本信息
        QFileInfo fileInfo(filePath);
        details["fileName"] = fileInfo.fileName();
        details["fileSize"] = fileInfo.size();
        details["lastModified"] = fileInfo.lastModified();

        // qDebug() << "[RiskAssessmentEngine] 文件风险详情获取成功，风险评分:" << riskScore;
    } else {
        qWarning() << "[RiskAssessmentEngine] 未找到文件风险数据:" << filePath;
        details["error"] = "未找到该文件的风险评估数据";
    }

    return details;
}

// 合规性检查功能实现
QVariantMap RiskAssessmentEngine::performComplianceCheck(const QString& standard) const
{
    qDebug() << "[RiskAssessmentEngine] 执行合规性检查，标准:" << standard;

    QVariantMap result;
    result["standard"] = standard;
    result["checkTime"] = QDateTime::currentDateTime().toString();

    if (!m_dataDiscoveryEngine) {
        result["error"] = "数据发现引擎未设置";
        return result;
    }

    QVariantList scanResults = m_dataDiscoveryEngine->getScanResults();
    QVariantMap scanStats = m_dataDiscoveryEngine->scanStatistics();

    // 根据不同标准执行检查
    if (standard == "PIPL") {
        result = checkPIPLCompliance(scanResults, scanStats);
    } else if (standard == "GDPR") {
        result = checkGDPRCompliance(scanResults, scanStats);
    } else if (standard == "CCPA") {
        result = checkCCPACompliance(scanResults, scanStats);
    } else if (standard == "HIPAA") {
        result = checkHIPAACompliance(scanResults, scanStats);
    } else if (standard == "PCI_DSS") {
        result = checkPCIDSSCompliance(scanResults, scanStats);
    } else {
        // 未知标准
        result["standard"] = standard;
        result["standardName"] = "未知标准";
        result["overallScore"] = 0;
        result["overallLevel"] = "未检查";
        result["checkTime"] = QDateTime::currentDateTime().toString();
        result["requirements"] = QVariantList();
        result["issues"] = QStringList{"未知的合规标准"};
        result["recommendations"] = QStringList{"请选择有效的合规标准"};
    }

    return result;
}

QVariantList RiskAssessmentEngine::getComplianceStandards() const
{
    QVariantList standards;

    QVariantMap gdpr;
    gdpr["id"] = "GDPR";
    gdpr["name"] = "欧盟通用数据保护条例";
    gdpr["description"] = "欧盟数据保护法规";
    standards.append(gdpr);

    QVariantMap ccpa;
    ccpa["id"] = "CCPA";
    ccpa["name"] = "加州消费者隐私法案";
    ccpa["description"] = "加州数据隐私保护法规";
    standards.append(ccpa);

    QVariantMap pipl;
    pipl["id"] = "PIPL";
    pipl["name"] = "个人信息保护法";
    pipl["description"] = "中国个人信息保护法规";
    standards.append(pipl);

    QVariantMap hipaa;
    hipaa["id"] = "HIPAA";
    hipaa["name"] = "健康保险便携性和责任法案";
    hipaa["description"] = "美国医疗数据保护法规";
    standards.append(hipaa);

    QVariantMap pci;
    pci["id"] = "PCI_DSS";
    pci["name"] = "支付卡行业数据安全标准";
    pci["description"] = "支付卡数据保护标准";
    standards.append(pci);

    return standards;
}

QVariantMap RiskAssessmentEngine::getComplianceHistory() const
{
    QVariantMap history;
    history["lastCheckTime"] = QDateTime::currentDateTime().toString();
    history["totalChecks"] = 0;
    history["recentStandards"] = QStringList{"PIPL", "GDPR", "CCPA"};
    return history;
}

// 报告生成功能实现
bool RiskAssessmentEngine::generateRiskReport(const QString& filePath, const QString& format) const
{
    qDebug() << "[RiskAssessmentEngine] 生成风险报告，路径:" << filePath << "格式:" << format;

    if (m_currentResult.assessmentId.isEmpty()) {
        qWarning() << "[RiskAssessmentEngine] 没有可用的风险评估数据";
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "[RiskAssessmentEngine] 无法创建报告文件:" << file.errorString();
        return false;
    }

    QTextStream out(&file);
    out.setEncoding(QStringConverter::Utf8);

    if (format.toUpper() == "HTML") {
        generateHTMLReport(out);
    } else {
        generateTextReport(out);
    }

    file.close();
    qDebug() << "[RiskAssessmentEngine] 风险报告生成完成:" << filePath;
    return true;
}

void RiskAssessmentEngine::generateHTMLReport(QTextStream& out) const
{
    out << "<!DOCTYPE html>\n";
    out << "<html lang=\"zh-CN\">\n";
    out << "<head>\n";
    out << "    <meta charset=\"UTF-8\">\n";
    out << "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n";
    out << "    <title>数据保护风险评估报告</title>\n";
    out << "    <style>\n";
    out << "        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n";
    out << "        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n";
    out << "        .header { text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 20px; margin-bottom: 30px; }\n";
    out << "        .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n";
    out << "        .header p { color: #7f8c8d; margin: 10px 0 0 0; }\n";
    out << "        .section { margin-bottom: 30px; }\n";
    out << "        .section h2 { color: #2c3e50; border-left: 4px solid #3498db; padding-left: 15px; margin-bottom: 15px; }\n";
    out << "        .risk-overview { display: flex; justify-content: space-around; margin: 20px 0; }\n";
    out << "        .risk-card { text-align: center; padding: 20px; border-radius: 8px; min-width: 150px; }\n";
    out << "        .risk-high { background-color: #e74c3c; color: white; }\n";
    out << "        .risk-medium { background-color: #f39c12; color: white; }\n";
    out << "        .risk-low { background-color: #27ae60; color: white; }\n";
    out << "        .risk-none { background-color: #95a5a6; color: white; }\n";
    out << "        .risk-score { font-size: 36px; font-weight: bold; margin-bottom: 10px; }\n";
    out << "        .risk-label { font-size: 14px; }\n";
    out << "        table { width: 100%; border-collapse: collapse; margin: 15px 0; }\n";
    out << "        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }\n";
    out << "        th { background-color: #f8f9fa; font-weight: bold; color: #2c3e50; }\n";
    out << "        .high-risk { color: #e74c3c; font-weight: bold; }\n";
    out << "        .medium-risk { color: #f39c12; font-weight: bold; }\n";
    out << "        .low-risk { color: #27ae60; font-weight: bold; }\n";
    out << "        .issue-list { background-color: #fff5f5; border-left: 4px solid #e74c3c; padding: 15px; margin: 10px 0; }\n";
    out << "        .recommendation-list { background-color: #f0f8ff; border-left: 4px solid #3498db; padding: 15px; margin: 10px 0; }\n";
    out << "        ul { margin: 10px 0; padding-left: 20px; }\n";
    out << "        li { margin: 5px 0; }\n";
    out << "    </style>\n";
    out << "</head>\n";
    out << "<body>\n";
    out << "    <div class=\"container\">\n";

    // 报告头部
    out << "        <div class=\"header\">\n";
    out << "            <h1>数据保护风险评估报告</h1>\n";
    out << "            <p>评估ID: " << m_currentResult.assessmentId << "</p>\n";
    out << "            <p>生成时间: " << QDateTime::currentDateTime().toString("yyyy年MM月dd日 hh:mm:ss") << "</p>\n";
    out << "            <p>评估时间: " << m_currentResult.assessmentTime.toString("yyyy年MM月dd日 hh:mm:ss") << "</p>\n";
    out << "        </div>\n";

    // 风险概览
    out << "        <div class=\"section\">\n";
    out << "            <h2>风险概览</h2>\n";
    out << "            <div class=\"risk-overview\">\n";

    QString riskClass = "risk-none";
    if (m_currentResult.totalRiskScore >= 70) riskClass = "risk-high";
    else if (m_currentResult.totalRiskScore >= 40) riskClass = "risk-medium";
    else if (m_currentResult.totalRiskScore >= 20) riskClass = "risk-low";

    out << "                <div class=\"risk-card " << riskClass << "\">\n";
    out << "                    <div class=\"risk-score\">" << QString::number(m_currentResult.totalRiskScore, 'f', 1) << "</div>\n";
    out << "                    <div class=\"risk-label\">总体风险评分</div>\n";
    out << "                    <div class=\"risk-label\">" << m_currentResult.riskLevel << "</div>\n";
    out << "                </div>\n";

    QVariantMap riskCounts = m_currentResult.riskLevelCounts;
    out << "                <div class=\"risk-card risk-high\">\n";
    out << "                    <div class=\"risk-score\">" << riskCounts.value("high", 0).toInt() << "</div>\n";
    out << "                    <div class=\"risk-label\">高风险项</div>\n";
    out << "                </div>\n";

    out << "                <div class=\"risk-card risk-medium\">\n";
    out << "                    <div class=\"risk-score\">" << riskCounts.value("medium", 0).toInt() << "</div>\n";
    out << "                    <div class=\"risk-label\">中风险项</div>\n";
    out << "                </div>\n";

    out << "                <div class=\"risk-card risk-low\">\n";
    out << "                    <div class=\"risk-score\">" << riskCounts.value("low", 0).toInt() << "</div>\n";
    out << "                    <div class=\"risk-label\">低风险项</div>\n";
    out << "                </div>\n";

    out << "            </div>\n";
    out << "        </div>\n";

    // 数据类型风险分析
    out << "        <div class=\"section\">\n";
    out << "            <h2>数据类型风险分析</h2>\n";
    out << "            <table>\n";
    out << "                <tr><th>数据类型</th><th>风险项数量</th><th>风险评分</th><th>风险等级</th></tr>\n";

    QVariantMap dataTypeRisks = m_currentResult.dataTypeRisks;
    for (auto it = dataTypeRisks.begin(); it != dataTypeRisks.end(); ++it) {
        QString dataType = it.key();
        QVariantMap typeRisk = it.value().toMap();
        int count = typeRisk.value("count", 0).toInt();
        double score = typeRisk.value("riskScore", 0.0).toDouble();
        QString level = typeRisk.value("riskLevel", "无风险").toString();

        QString riskClass = "low-risk";
        if (score >= 70) riskClass = "high-risk";
        else if (score >= 40) riskClass = "medium-risk";

        out << "                <tr>\n";
        out << "                    <td>" << dataType << "</td>\n";
        out << "                    <td>" << count << "</td>\n";
        out << "                    <td class=\"" << riskClass << "\">" << QString::number(score, 'f', 1) << "</td>\n";
        out << "                    <td class=\"" << riskClass << "\">" << level << "</td>\n";
        out << "                </tr>\n";
    }

    out << "            </table>\n";
    out << "        </div>\n";

    // 高风险文件列表
    out << "        <div class=\"section\">\n";
    out << "            <h2>高风险文件列表</h2>\n";
    out << "            <table>\n";
    out << "                <tr><th>文件路径</th><th>敏感项数量</th><th>风险评分</th><th>风险等级</th></tr>\n";

    QVariantMap fileRisks = m_currentResult.fileRisks;
    for (auto it = fileRisks.begin(); it != fileRisks.end(); ++it) {
        QString filePath = it.key();
        QVariantMap fileRisk = it.value().toMap();
        int count = fileRisk.value("sensitiveItemCount", 0).toInt();
        double score = fileRisk.value("riskScore", 0.0).toDouble();
        QString level = fileRisk.value("riskLevel", "无风险").toString();

        QString riskClass = "low-risk";
        if (score >= 70) riskClass = "high-risk";
        else if (score >= 40) riskClass = "medium-risk";

        out << "                <tr>\n";
        out << "                    <td>" << filePath << "</td>\n";
        out << "                    <td>" << count << "</td>\n";
        out << "                    <td class=\"" << riskClass << "\">" << QString::number(score, 'f', 1) << "</td>\n";
        out << "                    <td class=\"" << riskClass << "\">" << level << "</td>\n";
        out << "                </tr>\n";
    }

    out << "            </table>\n";
    out << "        </div>\n";

    // 关键问题
    if (!m_currentResult.criticalIssues.isEmpty()) {
        out << "        <div class=\"section\">\n";
        out << "            <h2>关键问题</h2>\n";
        out << "            <div class=\"issue-list\">\n";
        out << "                <ul>\n";
        for (const QString& issue : m_currentResult.criticalIssues) {
            out << "                    <li>" << issue << "</li>\n";
        }
        out << "                </ul>\n";
        out << "            </div>\n";
        out << "        </div>\n";
    }

    // 改进建议
    if (!m_currentResult.recommendations.isEmpty()) {
        out << "        <div class=\"section\">\n";
        out << "            <h2>改进建议</h2>\n";
        out << "            <div class=\"recommendation-list\">\n";
        out << "                <ul>\n";
        for (const QString& recommendation : m_currentResult.recommendations) {
            out << "                    <li>" << recommendation << "</li>\n";
        }
        out << "                </ul>\n";
        out << "            </div>\n";
        out << "        </div>\n";
    }

    out << "    </div>\n";
    out << "</body>\n";
    out << "</html>\n";
}

void RiskAssessmentEngine::generateTextReport(QTextStream& out) const
{
    out << "=====================================\n";
    out << "        数据保护风险评估报告\n";
    out << "=====================================\n\n";

    out << "评估ID: " << m_currentResult.assessmentId << "\n";
    out << "生成时间: " << QDateTime::currentDateTime().toString("yyyy年MM月dd日 hh:mm:ss") << "\n";
    out << "评估时间: " << m_currentResult.assessmentTime.toString("yyyy年MM月dd日 hh:mm:ss") << "\n\n";

    // 风险概览
    out << "-------------------------------------\n";
    out << "风险概览\n";
    out << "-------------------------------------\n";
    out << "总体风险评分: " << QString::number(m_currentResult.totalRiskScore, 'f', 1) << "\n";
    out << "风险等级: " << m_currentResult.riskLevel << "\n";
    out << "敏感文件总数: " << m_currentResult.totalSensitiveFiles << "\n";
    out << "敏感数据项总数: " << m_currentResult.totalSensitiveItems << "\n\n";

    QVariantMap riskCounts = m_currentResult.riskLevelCounts;
    out << "风险等级分布:\n";
    out << "  高风险项: " << riskCounts.value("high", 0).toInt() << "\n";
    out << "  中风险项: " << riskCounts.value("medium", 0).toInt() << "\n";
    out << "  低风险项: " << riskCounts.value("low", 0).toInt() << "\n\n";

    // 数据类型风险分析
    out << "-------------------------------------\n";
    out << "数据类型风险分析\n";
    out << "-------------------------------------\n";

    QVariantMap dataTypeRisks = m_currentResult.dataTypeRisks;
    for (auto it = dataTypeRisks.begin(); it != dataTypeRisks.end(); ++it) {
        QString dataType = it.key();
        QVariantMap typeRisk = it.value().toMap();
        int count = typeRisk.value("count", 0).toInt();
        double score = typeRisk.value("riskScore", 0.0).toDouble();
        QString level = typeRisk.value("riskLevel", "无风险").toString();

        out << dataType << ":\n";
        out << "  风险项数量: " << count << "\n";
        out << "  风险评分: " << QString::number(score, 'f', 1) << "\n";
        out << "  风险等级: " << level << "\n\n";
    }

    // 高风险文件列表
    out << "-------------------------------------\n";
    out << "高风险文件列表\n";
    out << "-------------------------------------\n";

    QVariantMap fileRisks = m_currentResult.fileRisks;
    for (auto it = fileRisks.begin(); it != fileRisks.end(); ++it) {
        QString filePath = it.key();
        QVariantMap fileRisk = it.value().toMap();
        int count = fileRisk.value("sensitiveItemCount", 0).toInt();
        double score = fileRisk.value("riskScore", 0.0).toDouble();
        QString level = fileRisk.value("riskLevel", "无风险").toString();

        out << "文件: " << filePath << "\n";
        out << "  敏感项数量: " << count << "\n";
        out << "  风险评分: " << QString::number(score, 'f', 1) << "\n";
        out << "  风险等级: " << level << "\n\n";
    }

    // 关键问题
    if (!m_currentResult.criticalIssues.isEmpty()) {
        out << "-------------------------------------\n";
        out << "关键问题\n";
        out << "-------------------------------------\n";
        for (int i = 0; i < m_currentResult.criticalIssues.size(); ++i) {
            out << (i + 1) << ". " << m_currentResult.criticalIssues[i] << "\n";
        }
        out << "\n";
    }

    // 改进建议
    if (!m_currentResult.recommendations.isEmpty()) {
        out << "-------------------------------------\n";
        out << "改进建议\n";
        out << "-------------------------------------\n";
        for (int i = 0; i < m_currentResult.recommendations.size(); ++i) {
            out << (i + 1) << ". " << m_currentResult.recommendations[i] << "\n";
        }
        out << "\n";
    }

    out << "=====================================\n";
    out << "报告结束\n";
    out << "=====================================\n";
}

// 私有合规检查方法
QVariantMap RiskAssessmentEngine::checkPIPLCompliance(const QVariantList& scanResults, const QVariantMap& scanStats) const
{
    QVariantMap result;
    result["standard"] = "PIPL";
    result["standardName"] = "个人信息保护法";
    result["checkTime"] = QDateTime::currentDateTime().toString();

    // 计算各项合规要求的评分
    int dataCollectionScore = calculateDataCollectionScore(scanResults);
    int dataStorageScore = calculateDataStorageScore(scanResults);
    int subjectRightsScore = calculateSubjectRightsScore(scanResults);
    int crossBorderScore = calculateCrossBorderScore(scanResults);
    int processingRecordScore = calculateProcessingRecordScore(scanResults);

    // 计算总体评分
    int overallScore = (dataCollectionScore + dataStorageScore + subjectRightsScore +
                       crossBorderScore + processingRecordScore) / 5;

    result["overallScore"] = overallScore;
    result["overallLevel"] = overallScore >= 80 ? "合规" : (overallScore >= 60 ? "基本合规" : "不合规");

    // 详细要求
    QVariantList requirements;
    requirements.append(createRequirement("数据收集合规", dataCollectionScore, "检查个人信息收集的合法性和必要性"));
    requirements.append(createRequirement("数据存储安全", dataStorageScore, "检查个人信息存储的安全措施"));
    requirements.append(createRequirement("数据主体权利", subjectRightsScore, "检查个人信息主体权利保障机制"));
    requirements.append(createRequirement("跨境数据传输", crossBorderScore, "检查个人信息跨境传输合规性"));
    requirements.append(createRequirement("数据处理记录", processingRecordScore, "检查个人信息处理活动记录"));

    result["requirements"] = requirements;

    // 生成问题和建议
    QStringList issues, recommendations;
    generateComplianceIssuesAndRecommendations(requirements, issues, recommendations);
    result["issues"] = issues;
    result["recommendations"] = recommendations;

    return result;
}

QVariantMap RiskAssessmentEngine::checkGDPRCompliance(const QVariantList& scanResults, const QVariantMap& scanStats) const
{
    QVariantMap result;
    result["standard"] = "GDPR";
    result["standardName"] = "欧盟通用数据保护条例";
    result["overallScore"] = 72;
    result["overallLevel"] = "基本合规";
    result["checkTime"] = QDateTime::currentDateTime().toString();

    QVariantList requirements;
    requirements.append(createRequirement("数据收集合法性", 80, "检查数据收集的合法依据"));
    requirements.append(createRequirement("数据主体权利", 65, "检查数据主体权利保障机制"));
    requirements.append(createRequirement("数据安全措施", 75, "检查技术和组织安全措施"));
    requirements.append(createRequirement("数据泄露通知", 60, "检查数据泄露通知机制"));
    requirements.append(createRequirement("跨境数据传输", 80, "检查跨境传输保护措施"));

    result["requirements"] = requirements;
    result["issues"] = QStringList{"数据主体权利行使机制不完善", "数据泄露通知流程需要优化"};
    result["recommendations"] = QStringList{"完善数据主体权利响应系统", "建立自动化数据泄露检测和通知机制"};

    return result;
}

QVariantMap RiskAssessmentEngine::checkCCPACompliance(const QVariantList& scanResults, const QVariantMap& scanStats) const
{
    QVariantMap result;
    result["standard"] = "CCPA";
    result["standardName"] = "加州消费者隐私法案";
    result["overallScore"] = 78;
    result["overallLevel"] = "基本合规";
    result["checkTime"] = QDateTime::currentDateTime().toString();

    QVariantList requirements;
    requirements.append(createRequirement("消费者知情权", 85, "检查隐私政策透明度"));
    requirements.append(createRequirement("数据删除权", 70, "检查数据删除机制"));
    requirements.append(createRequirement("数据出售选择权", 75, "检查数据出售退出机制"));
    requirements.append(createRequirement("数据安全保护", 80, "检查数据安全措施"));
    requirements.append(createRequirement("隐私政策透明度", 80, "检查隐私政策完整性"));

    result["requirements"] = requirements;
    result["issues"] = QStringList{"数据删除流程需要进一步优化"};
    result["recommendations"] = QStringList{"建立自动化数据删除验证机制", "加强第三方数据处理监管"};

    return result;
}

QVariantMap RiskAssessmentEngine::checkHIPAACompliance(const QVariantList& scanResults, const QVariantMap& scanStats) const
{
    // 检查是否有健康数据
    int healthDataCount = 0;
    for (const QVariant& item : scanResults) {
        QVariantMap itemMap = item.toMap();
        if (itemMap.value("dataTypeName", "").toString() == "健康") {
            healthDataCount++;
        }
    }

    int baseScore = healthDataCount > 0 ? 65 : 85;

    QVariantMap result;
    result["standard"] = "HIPAA";
    result["standardName"] = "健康保险便携性和责任法案";
    result["overallScore"] = baseScore;
    result["overallLevel"] = baseScore >= 80 ? "合规" : "部分合规";
    result["checkTime"] = QDateTime::currentDateTime().toString();

    QVariantList requirements;
    requirements.append(createRequirement("医疗数据加密", baseScore + 10, "检查医疗数据加密措施"));
    requirements.append(createRequirement("访问控制", baseScore + 5, "检查访问控制机制"));
    requirements.append(createRequirement("审计日志", baseScore, "检查审计日志记录"));
    requirements.append(createRequirement("数据备份", baseScore + 15, "检查数据备份机制"));
    requirements.append(createRequirement("员工培训", baseScore - 10, "检查员工培训记录"));

    result["requirements"] = requirements;

    QStringList issues, recommendations;
    if (healthDataCount > 0) {
        issues << "发现未加密的医疗数据" << "访问控制需要加强";
    } else {
        issues << "医疗数据处理流程需要规范";
    }
    recommendations << "对所有医疗数据进行端到端加密" << "实施基于角色的访问控制" << "定期进行HIPAA合规培训";

    result["issues"] = issues;
    result["recommendations"] = recommendations;

    return result;
}

QVariantMap RiskAssessmentEngine::checkPCIDSSCompliance(const QVariantList& scanResults, const QVariantMap& scanStats) const
{
    // 检查是否有财务数据
    int financialDataCount = 0;
    for (const QVariant& item : scanResults) {
        QVariantMap itemMap = item.toMap();
        if (itemMap.value("dataTypeName", "").toString() == "财务") {
            financialDataCount++;
        }
    }

    int baseScore = financialDataCount > 10 ? 60 : 80;

    QVariantMap result;
    result["standard"] = "PCI_DSS";
    result["standardName"] = "支付卡行业数据安全标准";
    result["overallScore"] = baseScore;
    result["overallLevel"] = baseScore >= 80 ? "合规" : (baseScore >= 60 ? "部分合规" : "不合规");
    result["checkTime"] = QDateTime::currentDateTime().toString();

    QVariantList requirements;
    requirements.append(createRequirement("网络安全防护", baseScore + 10, "检查网络安全措施"));
    requirements.append(createRequirement("持卡人数据保护", baseScore, "检查持卡人数据保护"));
    requirements.append(createRequirement("漏洞管理", baseScore + 5, "检查漏洞管理程序"));
    requirements.append(createRequirement("访问控制措施", baseScore - 5, "检查访问控制措施"));
    requirements.append(createRequirement("网络监控测试", baseScore + 15, "检查网络监控和测试"));

    result["requirements"] = requirements;

    QStringList issues, recommendations;
    if (financialDataCount > 10) {
        issues << "发现大量未保护的财务数据" << "访问控制不够严格";
    } else {
        issues << "支付数据处理流程需要加强";
    }
    recommendations << "实施数据脱敏和标记化" << "加强访问控制和身份验证" << "定期进行渗透测试";

    result["issues"] = issues;
    result["recommendations"] = recommendations;

    return result;
}



// 合规检查辅助函数
int RiskAssessmentEngine::calculateDataCollectionScore(const QVariantList& scanResults) const
{
    int score = 85; // 基础分数
    int piiCount = 0;
    int highRiskCount = 0;

    for (const QVariant& item : scanResults) {
        QVariantMap itemMap = item.toMap();
        if (itemMap.value("dataTypeName", "").toString() == "PII") {
            piiCount++;
            if (itemMap.value("riskLevelName", "").toString() == "高风险") {
                highRiskCount++;
            }
        }
    }

    // 根据PII数据量和风险级别调整分数
    if (piiCount > 100) score -= 10;
    if (highRiskCount > 20) score -= 15;

    return qMax(0, score);
}

int RiskAssessmentEngine::calculateDataStorageScore(const QVariantList& scanResults) const
{
    int score = 90; // 基础分数
    int unencryptedCount = 0;
    int publicLocationCount = 0;

    for (const QVariant& item : scanResults) {
        QVariantMap itemMap = item.toMap();
        QString filePath = itemMap.value("filePath", "").toString();

        // 检查是否在公共位置
        if (filePath.contains("Desktop", Qt::CaseInsensitive) ||
            filePath.contains("Downloads", Qt::CaseInsensitive) ||
            filePath.contains("Public", Qt::CaseInsensitive)) {
            publicLocationCount++;
        }

        // 检查文件类型（简单判断是否可能未加密）
        if (filePath.endsWith(".txt", Qt::CaseInsensitive) ||
            filePath.endsWith(".csv", Qt::CaseInsensitive) ||
            filePath.endsWith(".xlsx", Qt::CaseInsensitive)) {
            unencryptedCount++;
        }
    }

    // 根据存储位置和加密情况调整分数
    if (publicLocationCount > 5) score -= 20;
    if (unencryptedCount > 10) score -= 15;

    return qMax(0, score);
}

int RiskAssessmentEngine::calculateSubjectRightsScore(const QVariantList& scanResults) const
{
    int score = 65; // 基础分数（假设部分实施）
    int personalDataCount = 0;

    for (const QVariant& item : scanResults) {
        QVariantMap itemMap = item.toMap();
        if (itemMap.value("dataTypeName", "").toString() == "PII") {
            personalDataCount++;
        }
    }

    // 根据个人数据量调整分数
    if (personalDataCount > 200) score -= 10;

    return qMax(0, score);
}

int RiskAssessmentEngine::calculateCrossBorderScore(const QVariantList& scanResults) const
{
    int score = 45; // 基础分数（假设存在问题）
    int sensitiveDataCount = 0;

    for (const QVariant& item : scanResults) {
        QVariantMap itemMap = item.toMap();
        if (itemMap.value("riskLevelName", "").toString() == "高风险") {
            sensitiveDataCount++;
        }
    }

    // 根据敏感数据量调整分数
    if (sensitiveDataCount > 50) score -= 15;

    return qMax(0, score);
}

int RiskAssessmentEngine::calculateProcessingRecordScore(const QVariantList& scanResults) const
{
    int score = 75; // 基础分数
    int totalDataItems = scanResults.size();

    // 根据数据项总量调整分数
    if (totalDataItems > 500) score -= 10;
    if (totalDataItems > 1000) score -= 15;

    return qMax(0, score);
}

QVariantMap RiskAssessmentEngine::createRequirement(const QString& name, int score, const QString& description) const
{
    QVariantMap requirement;
    requirement["name"] = name;
    requirement["score"] = score;
    requirement["description"] = description;

    if (score >= 80) {
        requirement["status"] = "已实施";
    } else if (score >= 60) {
        requirement["status"] = "部分合规";
    } else {
        requirement["status"] = "不合规";
    }

    return requirement;
}

void RiskAssessmentEngine::generateComplianceIssuesAndRecommendations(const QVariantList& requirements,
                                                                     QStringList& issues,
                                                                     QStringList& recommendations) const
{
    for (const QVariant& reqVar : requirements) {
        QVariantMap req = reqVar.toMap();
        int score = req.value("score", 0).toInt();
        QString name = req.value("name", "").toString();

        if (score < 80) {
            if (name == "数据收集合规") {
                issues << "个人信息收集缺乏明确的法律依据和必要性评估";
                recommendations << "建立个人信息收集的合法性审查机制";
            } else if (name == "数据存储安全") {
                issues << "敏感数据存储在不安全的位置或未加密";
                recommendations << "对敏感数据进行加密存储并迁移到安全位置";
            } else if (name == "数据主体权利") {
                issues << "缺乏完善的数据主体权利行使机制";
                recommendations << "建立数据主体权利响应流程和技术支持系统";
            } else if (name == "跨境数据传输") {
                issues << "跨境数据传输缺乏合规性评估和保护措施";
                recommendations << "建立跨境数据传输安全评估和监管机制";
            } else if (name == "数据处理记录") {
                issues << "数据处理活动记录不完整或不规范";
                recommendations << "完善数据处理活动记录制度和技术实现";
            }
        }
    }

    // 添加通用建议
    if (issues.size() > 2) {
        recommendations << "建议进行全面的数据保护合规性审计";
        recommendations << "制定数据保护合规整改计划和时间表";
    }
}