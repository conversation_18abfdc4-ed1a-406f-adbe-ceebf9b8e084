{"version": "0.2.0", "configurations": [{"name": "QtBuild", "type": "cppvsdbg", "request": "launch", "program": "${command:cmake.launchTargetPath}", "args": [], "stopAtEntry": false, "cwd": "${workspaceRoot}", "environment": [{"name": "PATH", "value": "C:/Qt/Qt6/6.5.3/msvc2019_64/bin"}], "console": "integratedTerminal", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\1286d3dc108c73dc58cb4463b72db0e1\\tonka3000.qtvsctools\\qt.natvis.xml"}]}