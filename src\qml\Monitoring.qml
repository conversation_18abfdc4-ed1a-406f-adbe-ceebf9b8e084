import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

ScrollView {
    id: root
    anchors.fill: parent
    contentWidth: availableWidth
    
    ColumnLayout {
        width: parent.width
        anchors.margins: 20
        spacing: 20
        
        // 页面标题
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 8
                
                Text {
                    text: "👁️ 监控与响应"
                    font.pixelSize: 24
                    font.bold: true
                    color: Qt.color("#2c3e50")
                }
                
                Item { Layout.fillWidth: true }
                
                Rectangle {
                    Layout.preferredWidth: 12
                    Layout.preferredHeight: 12
                    radius: 6
                    color: Qt.color("#27ae60")
                    
                    SequentialAnimation on opacity {
                        running: true
                        loops: Animation.Infinite
                        NumberAnimation { to: 0.3; duration: 1000 }
                        NumberAnimation { to: 1.0; duration: 1000 }
                    }
                }
                
                Text {
                    text: "监控运行中"
                    color: Qt.color("#27ae60")
                    font.bold: true
                }
                
                Button {
                    text: "实时监控"
                    Material.background: Qt.color("#e74c3c")
                    Material.foreground: Qt.color("white")
                    onClicked: {
                        // 打开实时监控界面
                    }
                }
            }
        }
        
        // 监控概览仪表板
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 120
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 16
                spacing: 20
                
                // 活跃监控
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: Qt.color("#f8f9fa")
                    radius: 6
                    
                    ColumnLayout {
                        anchors.centerIn: parent
                        spacing: 8
                        
                        Text {
                            text: "活跃监控"
                            font.bold: true
                            horizontalAlignment: Text.AlignHCenter
                            Layout.alignment: Qt.AlignHCenter
                        }
                        
                        Text {
                            text: "24"
                            font.pixelSize: 32
                            font.bold: true
                            color: Qt.color("#3498db")
                            Layout.alignment: Qt.AlignHCenter
                        }
                        
                        Text {
                            text: "个监控点"
                            color: Qt.color("#7f8c8d")
                            Layout.alignment: Qt.AlignHCenter
                        }
                    }
                }
                
                // 今日告警
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: Qt.color("#f8f9fa")
                    radius: 6
                    
                    ColumnLayout {
                        anchors.centerIn: parent
                        spacing: 8
                        
                        Text {
                            text: "今日告警"
                            font.bold: true
                            horizontalAlignment: Text.AlignHCenter
                            Layout.alignment: Qt.AlignHCenter
                        }
                        
                        Text {
                            text: "7"
                            font.pixelSize: 32
                            font.bold: true
                            color: Qt.color("#e74c3c")
                            Layout.alignment: Qt.AlignHCenter
                        }
                        
                        Text {
                            text: "条告警"
                            color: Qt.color("#7f8c8d")
                            Layout.alignment: Qt.AlignHCenter
                        }
                    }
                }
                
                // 处理中事件
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: Qt.color("#f8f9fa")
                    radius: 6
                    
                    ColumnLayout {
                        anchors.centerIn: parent
                        spacing: 8
                        
                        Text {
                            text: "处理中事件"
                            font.bold: true
                            horizontalAlignment: Text.AlignHCenter
                            Layout.alignment: Qt.AlignHCenter
                        }
                        
                        Text {
                            text: "3"
                            font.pixelSize: 32
                            font.bold: true
                            color: Qt.color("#f39c12")
                            Layout.alignment: Qt.AlignHCenter
                        }
                        
                        Text {
                            text: "个事件"
                            color: Qt.color("#7f8c8d")
                            Layout.alignment: Qt.AlignHCenter
                        }
                    }
                }
                
                // 系统状态
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: Qt.color("#f8f9fa")
                    radius: 6
                    
                    ColumnLayout {
                        anchors.centerIn: parent
                        spacing: 8
                        
                        Text {
                            text: "系统状态"
                            font.bold: true
                            horizontalAlignment: Text.AlignHCenter
                            Layout.alignment: Qt.AlignHCenter
                        }
                        
                        Text {
                            text: "正常"
                            font.pixelSize: 20
                            font.bold: true
                            color: Qt.color("#27ae60")
                            Layout.alignment: Qt.AlignHCenter
                        }
                        
                        Text {
                            text: "运行时间: 72h"
                            color: Qt.color("#7f8c8d")
                            Layout.alignment: Qt.AlignHCenter
                        }
                    }
                }
            }
        }
        
        // 本地活动监控区域
        GroupBox {
            Layout.fillWidth: true
            title: "本地活动监控"
            
            ColumnLayout {
                anchors.fill: parent
                spacing: 12
                
                RowLayout {
                    Layout.fillWidth: true
                    
                    Text {
                        text: "监控范围:"
                        font.bold: true
                    }
                    
                    CheckBox {
                        text: "文件访问"
                        checked: true
                    }
                    
                    CheckBox {
                        text: "数据库操作"
                        checked: true
                    }
                    
                    CheckBox {
                        text: "网络传输"
                        checked: false
                    }
                    
                    CheckBox {
                        text: "系统调用"
                        checked: false
                    }
                    
                    Item { Layout.fillWidth: true }
                    
                    Button {
                        text: "配置监控"
                        onClicked: {
                            // 配置监控设置
                        }
                    }
                }
                
                // 实时活动列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(200, activityListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4
                    
                    ListView {
                        id: activityListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement { time: "14:32:15"; user: "Administrator"; action: "文件访问"; target: "C:\\Data\\customer.xlsx"; status: "允许"; risk: "中" }
                            ListElement { time: "14:31:48"; user: "System"; action: "数据库查询"; target: "UserDB.SELECT * FROM users"; status: "允许"; risk: "低" }
                            ListElement { time: "14:31:22"; user: "Guest"; action: "文件复制"; target: "C:\\Sensitive\\financial.pdf"; status: "阻止"; risk: "高" }
                            ListElement { time: "14:30:55"; user: "Administrator"; action: "文件删除"; target: "C:\\Temp\\cache.tmp"; status: "允许"; risk: "低" }
                            ListElement { time: "14:30:33"; user: "User001"; action: "数据导出"; target: "客户信息表"; status: "允许"; risk: "中" }
                        }
                        
                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")
                            
                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8
                                
                                Text { text: "时间"; font.bold: true; Layout.preferredWidth: 80 }
                                Text { text: "用户"; font.bold: true; Layout.preferredWidth: 100 }
                                Text { text: "操作"; font.bold: true; Layout.preferredWidth: 100 }
                                Text { text: "目标"; font.bold: true; Layout.fillWidth: true }
                                Text { text: "状态"; font.bold: true; Layout.preferredWidth: 60 }
                                Text { text: "风险"; font.bold: true; Layout.preferredWidth: 60 }
                            }
                        }
                        
                        delegate: Rectangle {
                            id: activeDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 35
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text { text: activeDelegateItem.model.time; Layout.preferredWidth: 80; font.family: "Consolas" }
                                Text { text: activeDelegateItem.model.user; Layout.preferredWidth: 100 }
                                Text { text: activeDelegateItem.model.action; Layout.preferredWidth: 100 }
                                Text { text: activeDelegateItem.model.target; Layout.fillWidth: true; font.family: "Consolas" }
                                Text {
                                    text: activeDelegateItem.model.status;
                                    Layout.preferredWidth: 60;
                                    color: activeDelegateItem.model.status === "允许" ? Qt.color("#27ae60") : Qt.color("#e74c3c")
                                }
                                Text {
                                    text: activeDelegateItem.model.risk;
                                    Layout.preferredWidth: 60;
                                    color: activeDelegateItem.model.risk === "高" ? Qt.color("#e74c3c") : (activeDelegateItem.model.risk === "中" ? Qt.color("#f39c12") : Qt.color("#27ae60"))
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // 基于规则的告警区域
        GroupBox {
            Layout.fillWidth: true
            title: "基于规则的告警"
            
            ColumnLayout {
                anchors.fill: parent
                spacing: 12
                
                RowLayout {
                    Layout.fillWidth: true
                    
                    Button {
                        text: "新建告警规则"
                        Material.background: Qt.color("#3498db")
                        Material.foreground: Qt.color("white")
                        onClicked: {
                            // 新建告警规则
                        }
                    }
                    
                    Button {
                        text: "规则模板"
                        onClicked: {
                            // 打开规则模板
                        }
                    }
                    
                    Item { Layout.fillWidth: true }
                    
                    Text {
                        text: "活跃规则: 15"
                        font.bold: true
                        color: Qt.color("#27ae60")
                    }
                    
                    Text {
                        text: "暂停规则: 3"
                        font.bold: true
                        color: Qt.color("#95a5a6")
                    }
                }
                
                // 告警规则列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(200, alertListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4
                    
                    ListView {
                        id: alertListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement { name: "敏感文件访问告警"; condition: "访问包含PII的文件"; severity: "高"; status: "启用"; triggered: "3次" }
                            ListElement { name: "大量数据导出告警"; condition: "单次导出>1000条记录"; severity: "中"; status: "启用"; triggered: "1次" }
                            ListElement { name: "非工作时间访问"; condition: "22:00-06:00时间段访问"; severity: "中"; status: "启用"; triggered: "0次" }
                            ListElement { name: "未授权用户访问"; condition: "Guest用户访问敏感目录"; severity: "高"; status: "暂停"; triggered: "5次" }
                        }
                        
                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")
                            
                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8
                                
                                Text { text: "规则名称"; font.bold: true; Layout.fillWidth: true }
                                Text { text: "触发条件"; font.bold: true; Layout.preferredWidth: 150 }
                                Text { text: "严重性"; font.bold: true; Layout.preferredWidth: 60 }
                                Text { text: "状态"; font.bold: true; Layout.preferredWidth: 60 }
                                Text { text: "触发次数"; font.bold: true; Layout.preferredWidth: 80 }
                            }
                        }
                        
                        delegate: Rectangle {
                            id: alertDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 35
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text { text: alertDelegateItem.model.name; Layout.fillWidth: true }
                                Text { text: alertDelegateItem.model.condition; Layout.preferredWidth: 150 }
                                Text {
                                    text: alertDelegateItem.model.severity;
                                    Layout.preferredWidth: 60;
                                    color: alertDelegateItem.model.severity === "高" ? Qt.color("#e74c3c") : Qt.color("#f39c12")
                                }
                                Text {
                                    text: alertDelegateItem.model.status;
                                    Layout.preferredWidth: 60;
                                    color: alertDelegateItem.model.status === "启用" ? Qt.color("#27ae60") : Qt.color("#95a5a6")
                                }
                                Text { text: alertDelegateItem.model.triggered; Layout.preferredWidth: 80 }
                            }
                        }
                    }
                }
            }
        }
        
        // 事件日志区域
        GroupBox {
            Layout.fillWidth: true
            title: "事件日志"
            
            ColumnLayout {
                anchors.fill: parent
                spacing: 12
                
                RowLayout {
                    Layout.fillWidth: true
                    
                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["所有事件", "安全事件", "访问事件", "系统事件", "错误事件"]
                        currentIndex: 0
                    }
                    
                    ComboBox {
                        Layout.preferredWidth: 100
                        model: ["今天", "本周", "本月", "自定义"]
                        currentIndex: 0
                    }
                    
                    TextField {
                        Layout.fillWidth: true
                        placeholderText: "搜索事件..."
                    }
                    
                    Button {
                        text: "导出日志"
                        onClicked: {
                            // 导出事件日志
                        }
                    }
                }
                
                // 事件日志列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(200, logListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4
                    
                    ListView {
                        id: logListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement { time: "2024-01-15 14:32:15"; level: "警告"; category: "安全事件"; message: "检测到敏感文件访问: customer.xlsx"; user: "Administrator" }
                            ListElement { time: "2024-01-15 14:31:48"; level: "信息"; category: "访问事件"; message: "数据库查询操作"; user: "System" }
                            ListElement { time: "2024-01-15 14:31:22"; level: "错误"; category: "安全事件"; message: "阻止未授权文件复制操作"; user: "Guest" }
                            ListElement { time: "2024-01-15 14:30:55"; level: "信息"; category: "系统事件"; message: "临时文件清理完成"; user: "Administrator" }
                            ListElement { time: "2024-01-15 14:30:33"; level: "警告"; category: "访问事件"; message: "大量数据导出操作"; user: "User001" }
                        }
                        
                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")
                            
                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8
                                
                                Text { text: "时间"; font.bold: true; Layout.preferredWidth: 140 }
                                Text { text: "级别"; font.bold: true; Layout.preferredWidth: 60 }
                                Text { text: "类别"; font.bold: true; Layout.preferredWidth: 80 }
                                Text { text: "消息"; font.bold: true; Layout.fillWidth: true }
                                Text { text: "用户"; font.bold: true; Layout.preferredWidth: 100 }
                            }
                        }
                        
                        delegate: Rectangle {
                            id: logDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 35
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8

                                Text { text: logDelegateItem.model.time; Layout.preferredWidth: 140; font.family: "Consolas" }
                                Text {
                                    text: logDelegateItem.model.level;
                                    Layout.preferredWidth: 60;
                                    color: logDelegateItem.model.level === "错误" ? Qt.color("#e74c3c") : (logDelegateItem.model.level === "警告" ? Qt.color("#f39c12") : Qt.color("#3498db"))
                                }
                                Text { text: logDelegateItem.model.category; Layout.preferredWidth: 80 }
                                Text { text: logDelegateItem.model.message; Layout.fillWidth: true }
                                Text { text: logDelegateItem.model.user; Layout.preferredWidth: 100 }
                            }
                        }
                    }
                }
            }
        }
        
        // 响应指导区域
        GroupBox {
            Layout.fillWidth: true
            title: "响应指导"
            
            ColumnLayout {
                anchors.fill: parent
                spacing: 12
                
                RowLayout {
                    Layout.fillWidth: true
                    
                    Text {
                        text: "当前威胁级别:"
                        font.bold: true
                    }
                    
                    Rectangle {
                        Layout.preferredWidth: 80
                        Layout.preferredHeight: 30
                        color: Qt.color("#f39c12")
                        radius: 4
                        
                        Text {
                            anchors.centerIn: parent
                            text: "中等"
                            color: Qt.color("white")
                            font.bold: true
                        }
                    }
                    
                    Item { Layout.fillWidth: true }
                    
                    Button {
                        text: "应急响应手册"
                        Material.background: Qt.color("#e74c3c")
                        Material.foreground: Qt.color("white")
                        onClicked: {
                            // 打开应急响应手册
                        }
                    }
                }
                
                // 响应建议
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(120, responseSuggestions.implicitHeight + 32)
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")
                    
                    ColumnLayout {
                        id: responseSuggestions
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 8
                        
                        Text {
                            text: "当前响应建议"
                            font.bold: true
                            font.pixelSize: 16
                        }
                        
                        Text {
                            text: "• 检查Guest用户的访问权限设置"
                            color: Qt.color("#e74c3c")
                        }
                        
                        Text {
                            text: "• 审查最近的敏感文件访问日志"
                            color: Qt.color("#f39c12")
                        }
                        
                        Text {
                            text: "• 考虑加强文件访问控制策略"
                            color: Qt.color("#3498db")
                        }
                        
                        Text {
                            text: "• 更新员工安全培训材料"
                            color: Qt.color("#3498db")
                        }
                    }
                }
            }
        }
        
        Item { Layout.preferredHeight: 20 }
    }
}
