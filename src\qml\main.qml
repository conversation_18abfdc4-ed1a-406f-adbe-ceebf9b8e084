import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15

pragma ComponentBehavior: Bound

ApplicationWindow {
    id: window
    width: 1400
    height: 900
    visible: true
    title: qsTr("数据安全与隐私管理平台")
    
    // 设置最小窗口大小
    minimumWidth: 1200
    minimumHeight: 800
    
    property int currentPageIndex: 0
    property var pageComponents: [
        "qrc:/src/qml/DataDiscovery.qml",
        "qrc:/src/qml/RiskAssessment.qml",
        "qrc:/src/qml/DataProtection.qml",
        "qrc:/src/qml/Monitoring.qml",
        "qrc:/src/qml/SystemManagement.qml"
    ]

    // 监听页面切换
    onCurrentPageIndexChanged: {
        console.log("页面切换到:", pageNames[currentPageIndex])
        // 延迟一点时间确保页面加载完成后再设置属性
        Qt.callLater(function() {
            if (pageLoader.item) {
                pageLoader.setupPageProperties()
            }
        })
    }
    
    property var pageNames: [
        "数据发现与分类",
        "风险评估与分析",
        "数据保护与控制", 
        "监控与响应",
        "系统管理"
    ]
    
    // 顶部标题栏
    header: Rectangle {
        height: 60
        color: Qt.color("#2c3e50")
        
        RowLayout {
            anchors.fill: parent
            anchors.margins: 16
            
            // 应用图标和标题
            RowLayout {
                spacing: 12
                
                Rectangle {
                    Layout.preferredWidth: 32
                    Layout.preferredHeight: 32
                    color: Qt.color("#3498db")
                    radius: 4
                    
                    Text {
                        anchors.centerIn: parent
                        text: "🛡️"
                        font.pixelSize: 18
                        color: Qt.color("white")
                    }
                }
                
                Text {
                    text: "数据安全与隐私管理平台"
                    font.pixelSize: 18
                    font.bold: true
                    color: Qt.color("white")
                }
            }
            
            Item { Layout.fillWidth: true }
            
            // 顶部工具栏按钮
            RowLayout {
                spacing: 8
                
                Button {
                    text: "设置"
                    flat: true
                    Material.foreground: Qt.color("white")
                    onClicked: {
                        // 设置功能
                    }
                }
                
                Button {
                    text: "帮助"
                    flat: true
                    Material.foreground: Qt.color("white")
                    onClicked: {
                        // 帮助功能
                    }
                }
                
                Button {
                    text: "关于"
                    flat: true
                    Material.foreground: Qt.color("white")
                    onClicked: {
                        // 关于功能
                    }
                }
            }
        }
    }
    
    // 主内容区域
    RowLayout {
        anchors.fill: parent
        spacing: 0
        
        // 左侧导航栏
        Rectangle {
            Layout.preferredWidth: 250
            Layout.fillHeight: true
            color: Qt.color("#34495e")
            
            ScrollView {
                anchors.fill: parent
                anchors.margins: 8
                
                Column {
                    width: parent.width - 16
                    spacing: 4
                    
                    Repeater {
                        model: window.pageNames.length
                        
                        Rectangle {
                            id: navItem
                            required property int index
                            width: parent.width
                            height: 48
                            radius: 6

                            // 根据状态设置背景色
                            color: {
                                if (window.currentPageIndex === navItem.index) {
                                    return Qt.color("#3498db")  // 选中状态：蓝色
                                } else if (mouseArea.containsMouse) {
                                    return Qt.color("#4a6741")  // 悬停状态：绿色
                                } else {
                                    return Qt.color("transparent")  // 默认状态：透明
                                }
                            }

                            MouseArea {
                                id: mouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                onClicked: {
                                    window.currentPageIndex = navItem.index
                                }
                            }
                            
                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 12
                                spacing: 12

                                // 图标
                                Rectangle {
                                    Layout.preferredWidth: 24
                                    Layout.preferredHeight: 24
                                    color: Qt.color("transparent")

                                    Text {
                                        anchors.centerIn: parent
                                        text: window.getModuleIcon(navItem.index)
                                        font.pixelSize: 16
                                        color: window.currentPageIndex === navItem.index ? Qt.color("white") : Qt.color("#bdc3c7")
                                    }
                                }

                                // 模块名称
                                Text {
                                    Layout.fillWidth: true
                                    text: window.pageNames[navItem.index]
                                    font.pixelSize: 14
                                    color: window.currentPageIndex === navItem.index ? "white" : Qt.color("#bdc3c7")
                                    wrapMode: Text.WordWrap
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // 分隔线
        Rectangle {
            Layout.preferredWidth: 1
            Layout.fillHeight: true
            color: Qt.color("#bdc3c7")
        }
        
        // 右侧主内容区域
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: Qt.color("#ecf0f1")
            
            Loader {
                id: pageLoader
                anchors.fill: parent
                source: window.pageComponents[window.currentPageIndex]

                onStatusChanged: {
                    if (status === Loader.Error) {
                        console.log("Failed to load:", source)
                    }
                }

                onLoaded: {
                    setupPageProperties()
                }

                function setupPageProperties() {
                    if (!item) return

                    // 为RiskAssessment页面设置riskEngine属性
                    if (window.currentPageIndex === 1) { // RiskAssessment是索引1
                        try {
                            item.riskEngine = riskAssessmentEngine
                            console.log("RiskAssessment页面已连接到风险评估引擎")
                        } catch (e) {
                            console.log("设置RiskAssessment引擎失败:", e)
                        }
                    }
                }
            }
        }
    }
    
    // 获取模块图标的函数
    function getModuleIcon(index) {
        var icons = ["🔍", "⚠️", "🔒", "📋", "👁️", "📊", "🔄", "⚙️"]
        return icons[index] || "📄"
    }
}
