#ifndef DATADISCOVERYENGINE_H
#define DATADISCOVERYENGINE_H

#include <QObject>
#include <QThread>
#include <QDir>
#include <QFileInfo>
#include <QRegularExpression>
#include <QStringList>
#include <QVariantMap>
#include <QVariantList>
#include <QMutex>
#include <QTimer>
#include <QThreadPool>
#include <QRunnable>
#include <QAtomicInt>
#include <QWaitCondition>
#include <QHash>
#include <QDateTime>

// LRU缓存模板类
template<typename Key, typename Value>
class LRUCache
{
public:
    explicit LRUCache(int maxSize = 1000) : m_maxSize(maxSize) {}

    bool contains(const Key& key) const {
        QMutexLocker locker(&m_mutex);
        return m_cache.contains(key);
    }

    Value get(const Key& key) {
        QMutexLocker locker(&m_mutex);
        auto it = m_cache.find(key);
        if (it != m_cache.end()) {
            // 移动到最前面（最近使用）
            m_accessOrder.removeOne(key);
            m_accessOrder.prepend(key);
            m_hitCount++;
            return it.value();
        }
        m_missCount++;
        return Value();
    }

    void put(const Key& key, const Value& value) {
        QMutexLocker locker(&m_mutex);

        if (m_cache.contains(key)) {
            // 更新现有项
            m_cache[key] = value;
            m_accessOrder.removeOne(key);
            m_accessOrder.prepend(key);
        } else {
            // 添加新项
            if (m_cache.size() >= m_maxSize) {
                // 移除最久未使用的项
                Key oldestKey = m_accessOrder.takeLast();
                m_cache.remove(oldestKey);
            }

            m_cache[key] = value;
            m_accessOrder.prepend(key);
        }
    }

    void clear() {
        QMutexLocker locker(&m_mutex);
        m_cache.clear();
        m_accessOrder.clear();
        m_hitCount = 0;
        m_missCount = 0;
    }

    int size() const {
        QMutexLocker locker(&m_mutex);
        return m_cache.size();
    }

    double hitRate() const {
        QMutexLocker locker(&m_mutex);
        int total = m_hitCount + m_missCount;
        return total > 0 ? (double)m_hitCount / total : 0.0;
    }

    void getStatistics(int& hits, int& misses, int& currentSize) const {
        QMutexLocker locker(&m_mutex);
        hits = m_hitCount;
        misses = m_missCount;
        currentSize = m_cache.size();
    }

private:
    mutable QMutex m_mutex;
    QHash<Key, Value> m_cache;
    QList<Key> m_accessOrder;
    int m_maxSize;
    int m_hitCount = 0;
    int m_missCount = 0;
};

// 缓存统计信息结构
struct CacheStatistics {
    int fileTypeCacheHits = 0;
    int fileTypeCacheMisses = 0;
    int fileTypeCacheSize = 0;
    int regexCacheHits = 0;
    int regexCacheMisses = 0;
    int regexCacheSize = 0;
    double fileTypeCacheHitRate = 0.0;
    double regexCacheHitRate = 0.0;
    QDateTime lastUpdated;

    CacheStatistics() : lastUpdated(QDateTime::currentDateTime()) {}
};

// 敏感数据类型枚举
enum class SensitiveDataType {
    PII,           // 个人身份信息
    Financial,     // 财务数据
    Health,        // 健康数据
    BusinessSecret,// 商业机密
    Other          // 其他
};

// 风险级别枚举
enum class RiskLevel {
    High,    // 高风险
    Medium,  // 中风险
    Low,     // 低风险
    None     // 无风险
};

// 优化的扫描规则结构
struct OptimizedScanRule {
    QString name;                    // 规则名称
    QString pattern;                 // 正则表达式模式
    SensitiveDataType dataType;      // 数据类型
    RiskLevel riskLevel;             // 风险级别
    bool enabled;                    // 是否启用
    QString description;             // 规则描述

    // 优化字段
    bool isSimplePattern;            // 是否为简单字符串匹配
    QString simpleKeyword;           // 简单关键词（用于快速预筛选）
    QRegularExpression compiledRegex; // 预编译的正则表达式

    OptimizedScanRule() : dataType(SensitiveDataType::Other), riskLevel(RiskLevel::Low),
                         enabled(true), isSimplePattern(false) {}

    OptimizedScanRule(const QString& n, const QString& p, SensitiveDataType dt, RiskLevel rl,
                     bool e = true, const QString& desc = "")
        : name(n), pattern(p), dataType(dt), riskLevel(rl), enabled(e), description(desc),
          isSimplePattern(false) {
        compileRule();
    }

    void compileRule();
};

// 为了向后兼容，使用类型别名
using ScanRule = OptimizedScanRule;

// 扫描结果项
struct ScanResultItem {
    QString filePath;                // 文件路径
    QString fileName;                // 文件名
    qint64 fileSize;                 // 文件大小
    QString ruleName;                // 匹配的规则名称
    QString matchedContent;          // 匹配的内容
    int lineNumber;                  // 行号
    int columnNumber;                // 列号
    SensitiveDataType dataType;      // 数据类型
    RiskLevel riskLevel;             // 风险级别
    QDateTime scanTime;              // 扫描时间
    
    ScanResultItem() : fileSize(0), lineNumber(0), columnNumber(0), 
                      dataType(SensitiveDataType::Other), riskLevel(RiskLevel::Low) {}
};

// 扫描统计信息
struct ScanStatistics {
    int totalFilesScanned;           // 总扫描文件数
    int totalSensitiveItems;         // 总敏感数据项数
    int piiCount;                    // PII数据数量
    int financialCount;              // 财务数据数量
    int healthCount;                 // 健康数据数量
    int businessSecretCount;         // 商业机密数量
    int otherCount;                  // 其他敏感数据数量
    int highRiskCount;               // 高风险项数量
    int mediumRiskCount;             // 中风险项数量
    int lowRiskCount;                // 低风险项数量
    QDateTime scanStartTime;         // 扫描开始时间
    QDateTime scanEndTime;           // 扫描结束时间

    ScanStatistics() : totalFilesScanned(0), totalSensitiveItems(0),
                      piiCount(0), financialCount(0), healthCount(0),
                      businessSecretCount(0), otherCount(0),
                      highRiskCount(0), mediumRiskCount(0), lowRiskCount(0) {}
};

// 扫描配置
struct ScanConfig {
    QString scanPath;                // 扫描路径
    bool includeSubdirectories;      // 包含子目录
    bool scanHiddenFiles;            // 扫描隐藏文件
    bool scanSystemFiles;            // 扫描系统文件
    QStringList fileExtensions;      // 文件扩展名包含列表（为空时使用排除列表）
    int maxFileSize;                 // 最大文件大小(MB)
    int maxScanDepth;                // 最大扫描深度
    int threadCount;                 // 线程数量

    // 优化配置选项
    bool enableMemoryMapping;        // 启用内存映射（大文件）
    bool enableRegexCache;           // 启用正则表达式缓存
    bool enableBatchProcessing;      // 启用批量处理
    int memoryMappingThreshold;      // 内存映射阈值(KB)
    int batchSize;                   // 批处理大小
    int bufferSize;                  // 缓冲区大小(KB)

    ScanConfig() : includeSubdirectories(true), scanHiddenFiles(false),
                  scanSystemFiles(false), maxFileSize(100), maxScanDepth(10),
                  threadCount(QThread::idealThreadCount()),
                  enableMemoryMapping(true), enableRegexCache(true),
                  enableBatchProcessing(true), memoryMappingThreshold(1024),
                  batchSize(50), bufferSize(8) {}
};

// 单个文件处理任务
class FileProcessTask : public QRunnable
{
    friend class DataDiscoveryEngine;  // 允许DataDiscoveryEngine访问私有成员
public:
    FileProcessTask(const QString& filePath, const QList<OptimizedScanRule>& rules,
                   const ScanConfig& config, QObject* parent);

    void run() override;

    void setResultCallback(std::function<void(const ScanResultItem&)> callback);
    void setProgressCallback(std::function<void(const QString&)> callback);
    void setStopFlag(QAtomicInt* stopFlag);

private:
    void processFile();
    void processFileContent(const QString& content);
    void processFileContentMapped(const char* data, qint64 size);
    void processLine(const QString& line, int lineNumber);
    void processBatchLines(const QStringList& lines, int startLineNumber);
    bool shouldScanFile(const QFileInfo& fileInfo);
    QString getFileContent();
    bool tryMemoryMappedRead();
    bool matchesRule(const QString& line, const OptimizedScanRule& rule,
                    QRegularExpressionMatch& match);

    QString m_filePath;
    QList<OptimizedScanRule> m_rules;
    ScanConfig m_config;
    QObject* m_parent;
    QAtomicInt* m_stopFlag;
    std::function<void(const ScanResultItem&)> m_resultCallback;
    std::function<void(const QString&)> m_progressCallback;

    // 优化的LRU缓存
    static LRUCache<QString, bool> s_mimeTypeCache;
    static LRUCache<QString, QRegularExpressionMatch> s_regexMatchCache;
    static QMutex s_cachesMutex;
};

// 优化的文件扫描工作线程
class FileScanWorker : public QObject
{
    Q_OBJECT
    friend class DataDiscoveryEngine;  // 允许DataDiscoveryEngine访问私有成员

public:
    explicit FileScanWorker(QObject *parent = nullptr);
    ~FileScanWorker();

    void setScanConfig(const ScanConfig& config);
    void setScanRules(const QList<OptimizedScanRule>& rules);
    void startScan();
    void stopScan();

signals:
    void scanProgress(int percentage, const QString& currentFile);
    void scanResultFound(const ScanResultItem& result);
    void scanCompleted(const ScanStatistics& statistics);
    void scanError(const QString& error);

private slots:
    void performScan();
    void onTaskCompleted(const QString& fileName);
    void onResultFound(const ScanResultItem& result);

private:
    void countTotalFiles(const QString& dirPath, int currentDepth = 0);
    void scanDirectory(const QString& dirPath, int currentDepth = 0);
    void collectFilePaths(const QString& dirPath, QStringList& filePaths, int currentDepth = 0);
    bool shouldScanFile(const QFileInfo& fileInfo);
    bool isLikelyTextFile(const QFileInfo& fileInfo);

    void updateProgress();

    ScanConfig m_config;
    QList<OptimizedScanRule> m_rules;
    ScanStatistics m_statistics;
    QMutex m_mutex;
    QMutex m_progressMutex;
    QAtomicInt m_shouldStop;
    QAtomicInt m_totalFiles;
    QAtomicInt m_processedFiles;
    QThreadPool* m_threadPool;
    QWaitCondition m_waitCondition;

    // 优化的LRU缓存
    LRUCache<QString, bool> m_fileTypeCache;
};

// 数据发现引擎主类
class DataDiscoveryEngine : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isScanning READ isScanning NOTIFY isScanningChanged)
    Q_PROPERTY(int scanProgress READ scanProgress NOTIFY scanProgressChanged)
    Q_PROPERTY(QString currentScanFile READ currentScanFile NOTIFY currentScanFileChanged)
    Q_PROPERTY(QVariantMap scanStatistics READ scanStatistics NOTIFY scanStatisticsChanged)

public:
    explicit DataDiscoveryEngine(QObject *parent = nullptr);
    ~DataDiscoveryEngine();

    // 属性访问器
    bool isScanning() const { return m_isScanning; }
    int scanProgress() const { return m_scanProgress; }
    QString currentScanFile() const { return m_currentScanFile; }
    QVariantMap scanStatistics() const;

    // 规则管理
    Q_INVOKABLE void addScanRule(const QString& name, const QString& pattern, 
                                int dataType, int riskLevel, bool enabled = true, 
                                const QString& description = "");
    Q_INVOKABLE void removeScanRule(const QString& name);
    Q_INVOKABLE void updateScanRule(const QString& name, const QString& pattern, 
                                   int dataType, int riskLevel, bool enabled, 
                                   const QString& description = "");
    Q_INVOKABLE QVariantList getScanRules() const;


    // 扫描控制
    Q_INVOKABLE void startScan(const QString& scanPath, bool includeSubdirs = true,
                              bool scanHidden = false, bool scanSystem = false);
    Q_INVOKABLE void stopScan();

    // 结果管理
    Q_INVOKABLE QVariantList getScanResults() const;
    Q_INVOKABLE QVariantList getFilteredResults(int dataType = -1, int riskLevel = -1) const;
    Q_INVOKABLE void clearScanResults();
    Q_INVOKABLE bool saveScanResults() const;
    Q_INVOKABLE bool loadScanResults();

    // 规则文件管理
    Q_INVOKABLE bool saveRulesToFile() const;
    Q_INVOKABLE bool loadRulesFromFile();

    // 缓存管理
    Q_INVOKABLE QVariantMap getCacheStatistics() const;
    Q_INVOKABLE void clearAllCaches();
    Q_INVOKABLE void optimizeCaches();

signals:
    void isScanningChanged();
    void scanProgressChanged();
    void currentScanFileChanged();
    void scanStatisticsChanged();
    void scanCompleted();
    void scanError(const QString& error);
    void newSensitiveDataFound(const QVariantMap& result);
    void scanRulesChanged();

private slots:
    void onScanProgress(int percentage, const QString& currentFile);
    void onScanResultFound(const ScanResultItem& result);
    void onScanCompleted(const ScanStatistics& statistics);
    void onScanError(const QString& error);

private:
    void initializeDefaultRules();
    void createDefaultRules();
    QString getRulesFilePath() const;
    QString getScanResultsFilePath() const;
    bool ensureDataDirectoryExists() const;
    QVariantMap scanResultItemToVariant(const ScanResultItem& item) const;
    QVariantMap scanRuleToVariant(const OptimizedScanRule& rule) const;
    OptimizedScanRule variantToScanRule(const QVariantMap& variant) const;
    QString dataTypeToString(SensitiveDataType type) const;
    QString riskLevelToString(RiskLevel level) const;
    SensitiveDataType intToDataType(int type) const;
    RiskLevel intToRiskLevel(int level) const;

    QList<OptimizedScanRule> m_scanRules;
    QList<ScanResultItem> m_scanResults;
    ScanStatistics m_currentStatistics;
    
    QThread* m_workerThread;
    FileScanWorker* m_worker;
    
    bool m_isScanning;
    int m_scanProgress;
    QString m_currentScanFile;
    
    mutable QMutex m_resultsMutex;
    mutable QMutex m_rulesMutex;
};

#endif // DATADISCOVERYENGINE_H
