#include "DataDiscoveryEngine.h"
#include <QDebug>
#include <QDateTime>
#include <QTextStream>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QStandardPaths>
#include <QCoreApplication>
#include <QMimeDatabase>
#include <QMimeType>
#include <QDir>
#include <QThreadPool>
#include <QAtomicInt>
#include <QWaitCondition>
#include <functional>
#include <algorithm>

// OptimizedScanRule 实现
void OptimizedScanRule::compileRule()
{
    if (!enabled || pattern.isEmpty()) {
        return;
    }

    // 检查是否为简单模式（只包含字母数字和基本字符，无正则特殊字符）
    static const QRegularExpression simplePatternCheck("^[\\w\\s\\-\\.@]+$");
    if (simplePatternCheck.match(pattern).hasMatch() &&
        !pattern.contains(QRegularExpression("[\\[\\]\\(\\)\\{\\}\\*\\+\\?\\|\\^\\$\\\\]"))) {
        isSimplePattern = true;
        simpleKeyword = pattern.toLower();
        // qDebug() << "规则" << name << "识别为简单模式，关键词:" << simpleKeyword;
    } else {
        isSimplePattern = false;
        compiledRegex.setPattern(pattern);
        compiledRegex.setPatternOptions(QRegularExpression::MultilineOption |
                                       QRegularExpression::UseUnicodePropertiesOption);
        if (!compiledRegex.isValid()) {
            qWarning() << "规则" << name << "正则表达式无效:" << compiledRegex.errorString();
            enabled = false;
        } else {
            // qDebug() << "规则" << name << "编译为正则表达式模式";
        }
    }
}

// FileProcessTask 静态成员初始化
LRUCache<QString, bool> FileProcessTask::s_mimeTypeCache(5000);  // 5000个文件类型缓存
LRUCache<QString, QRegularExpressionMatch> FileProcessTask::s_regexMatchCache(10000); // 10000个正则匹配缓存
QMutex FileProcessTask::s_cachesMutex;

// FileProcessTask 实现
FileProcessTask::FileProcessTask(const QString& filePath, const QList<OptimizedScanRule>& rules,
                               const ScanConfig& config, QObject* parent)
    : m_filePath(filePath), m_rules(rules), m_config(config), m_parent(parent), m_stopFlag(nullptr)
{
    setAutoDelete(true);
}

void FileProcessTask::run()
{
    if (m_stopFlag && m_stopFlag->loadAcquire()) {
        return;
    }

    processFile();

    if (m_progressCallback) {
        m_progressCallback(QFileInfo(m_filePath).fileName());
    }
}

void FileProcessTask::setResultCallback(std::function<void(const ScanResultItem&)> callback)
{
    m_resultCallback = callback;
}

void FileProcessTask::setProgressCallback(std::function<void(const QString&)> callback)
{
    m_progressCallback = callback;
}

void FileProcessTask::setStopFlag(QAtomicInt* stopFlag)
{
    m_stopFlag = stopFlag;
}

void FileProcessTask::processFile()
{
    QFileInfo fileInfo(m_filePath);

    if (!shouldScanFile(fileInfo)) {
        return;
    }

    // 检查文件大小限制
    if (fileInfo.size() > m_config.maxFileSize * 1024 * 1024) {
        return;
    }

    QElapsedTimer timer;
    timer.start();

    bool useMemoryMapping = false;

    // 对于大文件（>1MB），尝试使用内存映射
    if (fileInfo.size() > 1024 * 1024) {
        if (tryMemoryMappedRead()) {
            useMemoryMapping = true;
            // qDebug() << "文件" << fileInfo.fileName() << "使用内存映射处理，耗时:" << timer.elapsed() << "ms";
            return; // 内存映射成功，处理完成
        }
        // 内存映射失败，回退到传统方法
    }

    // 传统文件读取方法
    QString content = getFileContent();
    if (!content.isEmpty()) {
        processFileContent(content);
        // qDebug() << "文件" << fileInfo.fileName() << "使用传统方法处理，大小:"
        //          << fileInfo.size() << "字节，耗时:" << timer.elapsed() << "ms";
    }
}

bool FileProcessTask::shouldScanFile(const QFileInfo& fileInfo)
{
    // 检查隐藏文件
    if (!m_config.scanHiddenFiles && fileInfo.isHidden()) {
        return false;
    }

    // 检查系统文件 (Windows)
    if (!m_config.scanSystemFiles) {
        QString fileName = fileInfo.fileName();
        if (fileName.startsWith("$") || fileName.contains("System Volume Information")) {
            return false;
        }
    }

    // 优化的文件扩展名检查
    if (!m_config.fileExtensions.isEmpty()) {
        QString suffix = fileInfo.suffix().toLower();
        if (!m_config.fileExtensions.contains(suffix)) {
            return false;
        }
        return true; // 如果扩展名匹配，直接返回true，避免MIME检测
    }

    // 使用LRU缓存的MIME类型检测
    QString filePath = fileInfo.absoluteFilePath();
    if (s_mimeTypeCache.contains(filePath)) {
        return s_mimeTypeCache.get(filePath);
    }

    // 执行MIME检测并缓存结果
    QMimeDatabase mimeDb;
    QMimeType mimeType = mimeDb.mimeTypeForFile(fileInfo);
    bool shouldScan = mimeType.name().startsWith("text/") ||
                     mimeType.inherits("text/plain") ||
                     fileInfo.suffix().toLower().contains(QRegularExpression("txt|log|csv|json|xml|html|htm|js|css|cpp|h|py|java|cs"));

    // 缓存结果到LRU缓存
    s_mimeTypeCache.put(filePath, shouldScan);

    return shouldScan;
}

bool FileProcessTask::tryMemoryMappedRead()
{
    QFile file(m_filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    qint64 fileSize = file.size();
    if (fileSize == 0) {
        return true; // 空文件，处理完成
    }

    // 限制内存映射的最大文件大小（避免虚拟内存耗尽）
    const qint64 maxMappedSize = 100 * 1024 * 1024; // 100MB
    qint64 mappedSize = qMin(fileSize, maxMappedSize);

    uchar* mappedData = file.map(0, mappedSize);
    if (!mappedData) {
        // qDebug() << "内存映射失败，文件:" << m_filePath << "大小:" << fileSize;
        return false;
    }

    // qDebug() << "使用内存映射处理文件:" << m_filePath << "映射大小:" << mappedSize;

    try {
        // 处理映射的数据
        processFileContentMapped(reinterpret_cast<const char*>(mappedData), mappedSize);

        // 取消映射
        file.unmap(mappedData);
        return true;

    } catch (const std::exception& e) {
        qWarning() << "内存映射处理异常:" << e.what();
        file.unmap(mappedData);
        return false;
    }
}

void FileProcessTask::processFileContentMapped(const char* data, qint64 size)
{
    if (m_stopFlag && m_stopFlag->loadAcquire()) {
        return;
    }

    // 使用高效的流式处理方法处理内存映射数据
    const char* current = data;
    const char* end = data + size;
    const char* lineStart = current;
    int lineIndex = 0;

    // 缓冲区用于构建当前行
    QString currentLine;
    currentLine.reserve(1024); // 预分配空间

    while (current < end) {
        if (m_stopFlag && m_stopFlag->loadAcquire()) {
            break;
        }

        char ch = *current;

        if (ch == '\n' || ch == '\r') {
            // 处理行结束
            if (!currentLine.isEmpty()) {
                lineIndex++;
                processLine(currentLine, lineIndex);
                currentLine.clear();
            }

            // 处理Windows风格的\r\n
            if (ch == '\r' && current + 1 < end && *(current + 1) == '\n') {
                current++; // 跳过\n
            }
        } else {
            // 添加字符到当前行
            currentLine.append(QChar::fromLatin1(ch));

            // 防止行过长导致内存问题
            if (currentLine.length() > 10000) {
                lineIndex++;
                processLine(currentLine, lineIndex);
                currentLine.clear();
            }
        }

        current++;
    }

    // 处理最后一行（如果文件不以换行符结束）
    if (!currentLine.isEmpty()) {
        lineIndex++;
        processLine(currentLine, lineIndex);
    }
}

void FileProcessTask::processLine(const QString& line, int lineNumber)
{
    // 预筛选：跳过空行和过短的行
    if (line.trimmed().isEmpty() || line.length() < 3) {
        return;
    }

    // 延迟计算小写版本
    QString lowerLine;
    bool lowerLineComputed = false;

    for (const OptimizedScanRule& rule : m_rules) {
        if (!rule.enabled) continue;

        // 对于简单模式，延迟计算小写版本
        if (rule.isSimplePattern && !lowerLineComputed) {
            lowerLine = line.toLower();
            lowerLineComputed = true;
        }

        QRegularExpressionMatch match;
        if (matchesRule(line, rule, match)) {
            ScanResultItem result;
            result.filePath = m_filePath;
            result.fileName = QFileInfo(m_filePath).fileName();
            result.fileSize = QFileInfo(m_filePath).size();
            result.ruleName = rule.name;
            result.matchedContent = match.captured(0);
            result.lineNumber = lineNumber;
            result.columnNumber = match.capturedStart() + 1;
            result.dataType = rule.dataType;
            result.riskLevel = rule.riskLevel;
            result.scanTime = QDateTime::currentDateTime();

            if (m_resultCallback) {
                m_resultCallback(result);
            }
        }
    }
}

// FileScanWorker 实现
FileScanWorker::FileScanWorker(QObject *parent)
    : QObject(parent), m_shouldStop(0), m_totalFiles(0), m_processedFiles(0),
      m_threadPool(nullptr), m_fileTypeCache(3000)  // 3000个文件类型缓存
{
    m_threadPool = new QThreadPool(this);
}

FileScanWorker::~FileScanWorker()
{
    if (m_threadPool) {
        m_threadPool->clear();
        m_threadPool->waitForDone(5000);
    }
}

QString FileProcessTask::getFileContent()
{
    QFile file(m_filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return QString();
    }

    // 限制读取大小以避免内存问题
    const qint64 maxReadSize = 10 * 1024 * 1024; // 10MB
    QByteArray data;
    if (file.size() > maxReadSize) {
        data = file.read(maxReadSize);
    } else {
        data = file.readAll();
    }

    // 尝试多种编码方式读取文件
    QString content;

    // 首先尝试UTF-8
    QTextStream utf8Stream(data);
    utf8Stream.setEncoding(QStringConverter::Utf8);
    content = utf8Stream.readAll();

    // 检查是否包含替换字符，如果有则可能编码不正确
    if (content.contains(QChar::ReplacementCharacter)) {
        // 尝试系统默认编码
        QTextStream systemStream(data);
        systemStream.setEncoding(QStringConverter::System);
        content = systemStream.readAll();

        // 如果还是有问题，尝试本地编码
        if (content.contains(QChar::ReplacementCharacter)) {
            content = QString::fromLocal8Bit(data);
        }
    }

    return content;
}

void FileProcessTask::processFileContent(const QString& content)
{
    if (m_stopFlag && m_stopFlag->loadAcquire()) {
        return;
    }

    // 高级流式处理：使用缓冲区批量处理
    const int bufferSize = 8192; // 8KB缓冲区
    const QChar* data = content.constData();
    int contentLength = content.length();
    int currentPos = 0;
    int lineIndex = 0;

    QString lineBuffer;
    lineBuffer.reserve(1024); // 预分配行缓冲区

    // 批量处理缓冲区
    QStringList lineBatch;
    lineBatch.reserve(100); // 预分配批处理缓冲区

    while (currentPos < contentLength) {
        if (m_stopFlag && m_stopFlag->loadAcquire()) {
            break;
        }

        // 读取一个缓冲区的数据
        int endPos = qMin(currentPos + bufferSize, contentLength);

        for (int i = currentPos; i < endPos; ++i) {
            QChar ch = data[i];

            if (ch == '\n' || ch == '\r') {
                // 处理行结束
                if (!lineBuffer.isEmpty()) {
                    lineBatch.append(lineBuffer);
                    lineBuffer.clear();

                    // 批量处理行
                    if (lineBatch.size() >= 50) { // 每50行处理一次
                        processBatchLines(lineBatch, lineIndex - lineBatch.size() + 1);
                        lineBatch.clear();
                    }
                }

                // 处理Windows风格的\r\n
                if (ch == '\r' && i + 1 < endPos && data[i + 1] == '\n') {
                    i++; // 跳过\n
                }
                lineIndex++;
            } else {
                lineBuffer.append(ch);

                // 防止行过长
                if (lineBuffer.length() > 10000) {
                    lineBatch.append(lineBuffer);
                    lineBuffer.clear();
                    lineIndex++;

                    if (lineBatch.size() >= 50) {
                        processBatchLines(lineBatch, lineIndex - lineBatch.size() + 1);
                        lineBatch.clear();
                    }
                }
            }
        }

        currentPos = endPos;
    }

    // 处理最后一行
    if (!lineBuffer.isEmpty()) {
        lineBatch.append(lineBuffer);
        lineIndex++;
    }

    // 处理剩余的行
    if (!lineBatch.isEmpty()) {
        processBatchLines(lineBatch, lineIndex - lineBatch.size() + 1);
    }
}

void FileProcessTask::processBatchLines(const QStringList& lines, int startLineNumber)
{
    for (int i = 0; i < lines.size(); ++i) {
        if (m_stopFlag && m_stopFlag->loadAcquire()) {
            break;
        }

        const QString& line = lines[i];
        int lineNumber = startLineNumber + i;

        // 预筛选：跳过空行和过短的行
        if (line.trimmed().isEmpty() || line.length() < 3) {
            continue;
        }

        processLine(line, lineNumber);
    }
}

bool FileProcessTask::matchesRule(const QString& line, const OptimizedScanRule& rule,
                                 QRegularExpressionMatch& match)
{
    if (rule.isSimplePattern) {
        // 快速字符串匹配
        QString lowerLine = line.toLower();
        int pos = lowerLine.indexOf(rule.simpleKeyword);
        if (pos >= 0) {
            // 为简单匹配创建一个临时的正则表达式来获取正确的匹配信息
            QRegularExpression tempRegex(QRegularExpression::escape(rule.pattern));
            tempRegex.setPatternOptions(QRegularExpression::CaseInsensitiveOption);
            match = tempRegex.match(line);
            return match.hasMatch();
        }
        return false;
    } else {
        // 正则表达式匹配 - 使用缓存优化
        QString cacheKey = rule.name + "|" + line;

        // 检查缓存（对于重复出现的模式很有效）
        if (s_regexMatchCache.contains(cacheKey)) {
            match = s_regexMatchCache.get(cacheKey);
            return match.hasMatch();
        }

        // 执行正则匹配
        match = rule.compiledRegex.match(line);

        // 缓存结果（只缓存匹配的结果，减少缓存大小）
        if (match.hasMatch()) {
            s_regexMatchCache.put(cacheKey, match);
        }

        return match.hasMatch();
    }
}

void FileScanWorker::setScanConfig(const ScanConfig& config)
{
    m_config = config;

    // 设置线程池大小
    if (m_threadPool) {
        m_threadPool->setMaxThreadCount(config.threadCount);
        // qDebug() << "设置线程池大小为:" << config.threadCount;
    }
}

void FileScanWorker::setScanRules(const QList<OptimizedScanRule>& rules)
{
    m_rules = rules;

    // 确保所有规则都已编译
    for (OptimizedScanRule& rule : m_rules) {
        if (rule.enabled && !rule.compiledRegex.isValid() && !rule.isSimplePattern) {
            rule.compileRule();
        }
    }

    // qDebug() << "设置了" << m_rules.size() << "个优化规则";
    int simpleCount = 0;
    int regexCount = 0;
    for (const OptimizedScanRule& rule : m_rules) {
        if (rule.enabled) {
            if (rule.isSimplePattern) {
                simpleCount++;
            } else {
                regexCount++;
            }
        }
    }
    // qDebug() << "其中简单模式:" << simpleCount << "个，正则模式:" << regexCount << "个";
}



void FileScanWorker::startScan()
{
    m_shouldStop.storeRelease(0);
    m_statistics = ScanStatistics();
    m_statistics.scanStartTime = QDateTime::currentDateTime();
    m_totalFiles.storeRelease(0);
    m_processedFiles.storeRelease(0);

    performScan();
}

void FileScanWorker::stopScan()
{
    m_shouldStop.storeRelease(1);

    if (m_threadPool) {
        m_threadPool->clear();
        m_threadPool->waitForDone(3000);
    }
}

void FileScanWorker::performScan()
{
    if (m_config.scanPath.isEmpty()) {
        emit scanError("扫描路径不能为空");
        return;
    }

    QDir scanDir(m_config.scanPath);
    if (!scanDir.exists()) {
        emit scanError("扫描路径不存在: " + m_config.scanPath);
        return;
    }

    // qDebug() << "开始多线程扫描，使用" << m_rules.size() << "个优化规则，线程数:" << m_config.threadCount;

    try {
        // 收集所有需要扫描的文件路径
        QStringList filePaths;
        collectFilePaths(m_config.scanPath, filePaths, 0);

        m_totalFiles.storeRelease(filePaths.size());
        // qDebug() << "找到" << filePaths.size() << "个文件需要扫描";

        if (filePaths.isEmpty()) {
            m_statistics.scanEndTime = QDateTime::currentDateTime();
            emit scanCompleted(m_statistics);
            return;
        }

        // 为每个文件创建处理任务
        for (const QString& filePath : filePaths) {
            if (m_shouldStop.loadAcquire()) {
                break;
            }

            FileProcessTask* task = new FileProcessTask(filePath, m_rules, m_config, this);

            // 设置回调函数
            task->setResultCallback([this](const ScanResultItem& result) {
                this->onResultFound(result);
            });

            task->setProgressCallback([this](const QString& fileName) {
                this->onTaskCompleted(fileName);
            });

            task->setStopFlag(&m_shouldStop);

            m_threadPool->start(task);
        }

        // 等待所有任务完成
        m_threadPool->waitForDone();

        m_statistics.scanEndTime = QDateTime::currentDateTime();
        emit scanCompleted(m_statistics);

    } catch (const std::exception& e) {
        emit scanError(QString("扫描过程中发生错误: %1").arg(e.what()));
    }
}

void FileScanWorker::collectFilePaths(const QString& dirPath, QStringList& filePaths, int currentDepth)
{
    if (m_shouldStop.loadAcquire() || currentDepth > m_config.maxScanDepth) {
        return;
    }

    QDir dir(dirPath);
    QFileInfoList entries = dir.entryInfoList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    for (const QFileInfo& entry : entries) {
        if (m_shouldStop.loadAcquire()) break;

        if (entry.isFile() && shouldScanFile(entry)) {
            filePaths.append(entry.absoluteFilePath());
        } else if (entry.isDir() && m_config.includeSubdirectories) {
            collectFilePaths(entry.absoluteFilePath(), filePaths, currentDepth + 1);
        }
    }
}

void FileScanWorker::onTaskCompleted(const QString& fileName)
{
    int processed = m_processedFiles.fetchAndAddAcquire(1) + 1;
    int total = m_totalFiles.loadAcquire();

    updateProgress();
}

void FileScanWorker::onResultFound(const ScanResultItem& result)
{
    {
        QMutexLocker locker(&m_mutex);

        // 更新统计信息（线程安全）
        m_statistics.totalSensitiveItems++;
        switch (result.dataType) {
            case SensitiveDataType::PII:
                m_statistics.piiCount++;
                break;
            case SensitiveDataType::Financial:
                m_statistics.financialCount++;
                break;
            case SensitiveDataType::Health:
                m_statistics.healthCount++;
                break;
            case SensitiveDataType::BusinessSecret:
                m_statistics.businessSecretCount++;
                break;
            default:
                m_statistics.otherCount++;
                break;
        }

        switch (result.riskLevel) {
            case RiskLevel::High:
                m_statistics.highRiskCount++;
                break;
            case RiskLevel::Medium:
                m_statistics.mediumRiskCount++;
                break;
            case RiskLevel::Low:
                m_statistics.lowRiskCount++;
                break;
            default:
                break;
        }

        // 更新已扫描文件数（每个结果代表至少扫描了一个文件）
        m_statistics.totalFilesScanned = m_processedFiles.loadAcquire();
    }

    emit scanResultFound(result);
}

void FileScanWorker::updateProgress()
{
    QMutexLocker locker(&m_progressMutex);

    int processed = m_processedFiles.loadAcquire();
    int total = m_totalFiles.loadAcquire();

    if (total > 0) {
        int progress = (processed * 100) / total;
        emit scanProgress(progress, QString("已处理 %1/%2 个文件").arg(processed).arg(total));
    }
}

// 保留原有的countTotalFiles方法以保持兼容性
void FileScanWorker::countTotalFiles(const QString& dirPath, int currentDepth)
{
    QStringList tempPaths;
    collectFilePaths(dirPath, tempPaths, currentDepth);
    m_totalFiles.storeRelease(tempPaths.size());
}

void FileScanWorker::scanDirectory(const QString& dirPath, int currentDepth)
{
    // 这个方法现在主要用于兼容性，实际扫描使用collectFilePaths + 多线程处理
    if (m_shouldStop.loadAcquire() || currentDepth > m_config.maxScanDepth) {
        return;
    }

    QDir dir(dirPath);
    QFileInfoList entries = dir.entryInfoList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    for (const QFileInfo& entry : entries) {
        if (m_shouldStop.loadAcquire()) break;

        if (entry.isFile()) {
            if (shouldScanFile(entry)) {
                // 在新的多线程架构中，这里不再直接处理文件
                // 文件处理由FileProcessTask负责
            }
        } else if (entry.isDir() && m_config.includeSubdirectories) {
            scanDirectory(entry.absoluteFilePath(), currentDepth + 1);
        }
    }
}

bool FileScanWorker::shouldScanFile(const QFileInfo& fileInfo)
{
    // 检查隐藏文件
    if (!m_config.scanHiddenFiles && fileInfo.isHidden()) {
        return false;
    }

    // 检查系统文件 (Windows)
    if (!m_config.scanSystemFiles) {
        QString fileName = fileInfo.fileName();
        if (fileName.startsWith("$") || fileName.contains("System Volume Information")) {
            return false;
        }
    }

    // 优化的文件扩展名检查 - 使用排除列表
    QString suffix = fileInfo.suffix().toLower();

    // 如果配置了包含列表，优先使用包含列表（向后兼容）
    if (!m_config.fileExtensions.isEmpty()) {
        if (!m_config.fileExtensions.contains(suffix)) {
            return false;
        }
        return true; // 如果扩展名匹配，直接返回true，避免MIME检测
    }

    // 使用排除列表进行过滤
    static const QStringList excludeExtensions = {
        // 可执行文件
        "exe", "dll", "so", "dylib", "bin", "app", "msi", "deb", "rpm", "pkg", "dmg",

        // 图像文件
        "jpg", "jpeg", "png", "gif", "bmp", "tiff", "tif", "webp", "ico", "psd", "ai", "eps",

        // 音频文件
        "mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus",

        // 视频文件
        "mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp", "ogv",

        // 压缩文件
        "zip", "rar", "7z", "tar", "gz", "bz2", "xz", "lzma", "cab", "iso",

        // 字体文件
        "ttf", "otf", "woff", "woff2", "eot",

        // 数据库文件
        "db", "sqlite", "sqlite3", "mdb", "accdb",

        // Office二进制格式
        "doc", "xls", "ppt", "docx", "xlsx", "pptx", "pdf",

        // 编译产物
        "o", "obj", "lib", "a", "class", "pyc", "pyo", "jar"
    };

    // 如果在排除列表中，直接返回false
    if (excludeExtensions.contains(suffix)) {
        return false;
    }

    // 使用LRU缓存的MIME类型检测
    QString filePath = fileInfo.absoluteFilePath();
    if (m_fileTypeCache.contains(filePath)) {
        return m_fileTypeCache.get(filePath);
    }

    // 执行MIME检测并缓存结果
    QMimeDatabase mimeDb;
    QMimeType mimeType = mimeDb.mimeTypeForFile(fileInfo);

    // 更全面的文本文件检测
    bool shouldScan = false;

    // 1. 基于MIME类型的检测
    if (mimeType.name().startsWith("text/") ||
        mimeType.inherits("text/plain") ||
        mimeType.name().contains("json") ||
        mimeType.name().contains("xml") ||
        mimeType.name().contains("javascript") ||
        mimeType.name().contains("css") ||
        mimeType.name().contains("html")) {
        shouldScan = true;
    }

    // 2. 对于无扩展名文件或MIME检测不准确的情况，进行额外检查
    if (!shouldScan) {
        // 检查是否为常见的文本文件扩展名
        static const QStringList textExtensions = {
            // 基础文本文件
            "txt", "text", "md", "markdown", "rst", "log", "out",

            // 配置文件
            "ini", "conf", "config", "cfg", "properties", "toml", "env",

            // 数据文件
            "csv", "tsv", "json", "xml", "yaml", "yml", "jsonl",

            // Web文件
            "html", "htm", "css", "js", "jsx", "ts", "tsx", "vue", "svelte",

            // 编程语言文件
            "c", "cpp", "cxx", "cc", "h", "hpp", "hxx", "hh",
            "py", "pyw", "java", "kt", "scala", "groovy",
            "cs", "vb", "fs", "php", "rb", "go", "rs", "swift",
            "pl", "pm", "r", "m", "mm", "lua", "tcl", "sh", "bash", "zsh", "fish",
            "bat", "cmd", "ps1", "psm1", "psd1",

            // 标记语言
            "tex", "latex", "rtf", "org", "adoc", "asciidoc",

            // 项目文件
            "cmake", "make", "makefile", "dockerfile", "vagrantfile",
            "gitignore", "gitattributes", "editorconfig",

            // Qt/QML文件
            "qml", "qrc", "pro", "pri", "prf",

            // 其他可能包含敏感信息的文件
            "sql", "ddl", "dml", "key", "pem", "crt", "cert", "pub"
        };

        shouldScan = textExtensions.contains(suffix);
    }

    // 3. 对于无扩展名的文件，尝试读取文件头部判断
    if (!shouldScan && suffix.isEmpty()) {
        shouldScan = isLikelyTextFile(fileInfo);
    }

    // 缓存结果到LRU缓存（自动管理大小）
    m_fileTypeCache.put(filePath, shouldScan);

    return shouldScan;
}

bool FileScanWorker::isLikelyTextFile(const QFileInfo& fileInfo)
{
    // 对于无扩展名文件，读取文件头部判断是否为文本文件
    QFile file(fileInfo.absoluteFilePath());
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    // 读取文件前1024字节进行判断
    QByteArray header = file.read(1024);
    if (header.isEmpty()) {
        return false;
    }

    // 检查是否包含过多的二进制字符
    int binaryCount = 0;
    int totalCount = header.size();

    for (char byte : header) {
        unsigned char ub = static_cast<unsigned char>(byte);

        // 检查是否为二进制字符（排除常见的控制字符）
        if (ub < 32 && ub != 9 && ub != 10 && ub != 13) { // 排除tab, LF, CR
            binaryCount++;
        } else if (ub > 126 && ub < 160) { // 扩展ASCII中的控制字符
            binaryCount++;
        }
    }

    // 如果二进制字符比例超过5%，认为是二进制文件
    double binaryRatio = (double)binaryCount / totalCount;
    bool isText = binaryRatio < 0.05;

    // qDebug() << "无扩展名文件" << fileInfo.fileName()
    //          << "二进制字符比例:" << (binaryRatio * 100) << "% "
    //          << "判定为:" << (isText ? "文本文件" : "二进制文件");

    return isText;
}

// 这些方法现在由FileProcessTask处理，保留空实现以保持兼容性
// 或者可以完全删除，因为新的多线程架构不再使用这些方法

// DataDiscoveryEngine 实现
DataDiscoveryEngine::DataDiscoveryEngine(QObject *parent)
    : QObject(parent), m_workerThread(nullptr), m_worker(nullptr),
      m_isScanning(false), m_scanProgress(0)
{
    initializeDefaultRules();
    loadScanResults();
}

DataDiscoveryEngine::~DataDiscoveryEngine()
{
    if (m_workerThread && m_workerThread->isRunning()) {
        stopScan();
        m_workerThread->wait(5000);
    }
    
    delete m_worker;
    delete m_workerThread;
}

void DataDiscoveryEngine::initializeDefaultRules()
{
    // 首先尝试从文件加载规则
    if (!loadRulesFromFile()) {
        // 如果文件不存在或加载失败，创建默认规则并保存
        // qDebug() << "无法从文件加载规则，创建默认规则";
        createDefaultRules();
        saveRulesToFile();
    } else {
        // qDebug() << "成功从文件加载规则";
    }
}

QVariantMap DataDiscoveryEngine::scanStatistics() const
{
    QVariantMap stats;
    stats["totalFilesScanned"] = m_currentStatistics.totalFilesScanned;
    stats["totalSensitiveItems"] = m_currentStatistics.totalSensitiveItems;
    stats["piiCount"] = m_currentStatistics.piiCount;
    stats["financialCount"] = m_currentStatistics.financialCount;
    stats["healthCount"] = m_currentStatistics.healthCount;
    stats["businessSecretCount"] = m_currentStatistics.businessSecretCount;
    stats["otherCount"] = m_currentStatistics.otherCount;
    stats["highRiskCount"] = m_currentStatistics.highRiskCount;
    stats["mediumRiskCount"] = m_currentStatistics.mediumRiskCount;
    stats["lowRiskCount"] = m_currentStatistics.lowRiskCount;
    stats["scanStartTime"] = m_currentStatistics.scanStartTime;
    stats["scanEndTime"] = m_currentStatistics.scanEndTime;

    return stats;
}

void DataDiscoveryEngine::addScanRule(const QString& name, const QString& pattern,
                                     int dataType, int riskLevel, bool enabled,
                                     const QString& description)
{
    // qDebug() << "DataDiscoveryEngine::addScanRule called with name:" << name;

    bool shouldEmitSignal = false;
    {
        QMutexLocker locker(&m_rulesMutex);

        OptimizedScanRule rule(name, pattern, intToDataType(dataType), intToRiskLevel(riskLevel), enabled, description);

        // 检查规则是否已存在
        bool ruleExists = false;
        for (int i = 0; i < m_scanRules.size(); ++i) {
            if (m_scanRules[i].name == name) {
                m_scanRules[i] = rule;
                shouldEmitSignal = true;
                ruleExists = true;
                // qDebug() << "Updated existing rule:" << name;
                break;
            }
        }

        if (!ruleExists) {
            m_scanRules.append(rule);
            shouldEmitSignal = true;
            // qDebug() << "Added new rule:" << name << "Total rules:" << m_scanRules.size();
        }
    }

    // 在锁外发射信号，避免死锁
    if (shouldEmitSignal) {
        // qDebug() << "Emitting scanRulesChanged signal";
        emit scanRulesChanged();
        // 保存规则到文件
        saveRulesToFile();
    }
}

void DataDiscoveryEngine::removeScanRule(const QString& name)
{
    // qDebug() << "DataDiscoveryEngine::removeScanRule called with name:" << name;

    bool shouldEmitSignal = false;
    {
        QMutexLocker locker(&m_rulesMutex);

        for (int i = 0; i < m_scanRules.size(); ++i) {
            if (m_scanRules[i].name == name) {
                m_scanRules.removeAt(i);
                shouldEmitSignal = true;
                // qDebug() << "Removed rule:" << name << "Remaining rules:" << m_scanRules.size();
                break;
            }
        }

        if (!shouldEmitSignal) {
            // qDebug() << "Rule not found:" << name;
        }
    }

    // 在锁外发射信号，避免死锁
    if (shouldEmitSignal) {
        // qDebug() << "Emitting scanRulesChanged signal";
        emit scanRulesChanged();
        // 保存规则到文件
        saveRulesToFile();
    }
}

void DataDiscoveryEngine::updateScanRule(const QString& name, const QString& pattern,
                                        int dataType, int riskLevel, bool enabled,
                                        const QString& description)
{
    addScanRule(name, pattern, dataType, riskLevel, enabled, description);
}

QVariantList DataDiscoveryEngine::getScanRules() const
{
    QList<OptimizedScanRule> rulesCopy;
    {
        QMutexLocker locker(&m_rulesMutex);
        rulesCopy = m_scanRules;  // 创建副本
    }

    QVariantList rules;
    for (const auto& rule : rulesCopy) {
        rules.append(scanRuleToVariant(rule));
    }
    return rules;
}

void DataDiscoveryEngine::createDefaultRules()
{
    QMutexLocker locker(&m_rulesMutex);

    m_scanRules.clear();

    // 身份证号码
    m_scanRules.append(OptimizedScanRule("身份证号码", "\\b\\d{17}[\\dXx]\\b",
                               SensitiveDataType::PII, RiskLevel::High, true,
                               "中国大陆身份证号码识别"));

    // 手机号码
    m_scanRules.append(OptimizedScanRule("手机号码", "\\b1[3-9]\\d{9}\\b",
                               SensitiveDataType::PII, RiskLevel::Medium, true,
                               "中国大陆手机号码识别"));

    // 邮箱地址
    m_scanRules.append(OptimizedScanRule("邮箱地址", "\\b[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}\\b",
                               SensitiveDataType::PII, RiskLevel::Medium, true,
                               "电子邮箱地址识别"));

    // 银行卡号
    m_scanRules.append(OptimizedScanRule("银行卡号", "\\b\\d{16,19}\\b",
                               SensitiveDataType::Financial, RiskLevel::High, false,
                               "银行卡号识别"));

    // IP地址
    m_scanRules.append(OptimizedScanRule("IP地址", "\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b",
                               SensitiveDataType::Other, RiskLevel::Low, true,
                               "IPv4地址识别"));

    // 密码字段
    m_scanRules.append(OptimizedScanRule("密码字段", "(?i)(password|pwd|pass)\\s*[:=]\\s*[\"']?([^\\s\"']+)",
                               SensitiveDataType::BusinessSecret, RiskLevel::High, true,
                               "密码字段识别"));

    // API密钥
    m_scanRules.append(OptimizedScanRule("API密钥", "(?i)(api[_-]?key|apikey|access[_-]?token)\\s*[:=]\\s*[\"']?([a-zA-Z0-9]{20,})",
                               SensitiveDataType::BusinessSecret, RiskLevel::High, true,
                               "API密钥和访问令牌识别"));
}

void DataDiscoveryEngine::startScan(const QString& scanPath, bool includeSubdirs,
                                   bool scanHidden, bool scanSystem)
{
    if (m_isScanning) {
        qWarning() << "扫描已在进行中";
        return;
    }

    // 创建工作线程
    m_workerThread = new QThread(this);
    m_worker = new FileScanWorker();
    m_worker->moveToThread(m_workerThread);

    // 连接信号
    connect(m_workerThread, &QThread::started, m_worker, &FileScanWorker::startScan);
    connect(m_worker, &FileScanWorker::scanProgress, this, &DataDiscoveryEngine::onScanProgress);
    connect(m_worker, &FileScanWorker::scanResultFound, this, &DataDiscoveryEngine::onScanResultFound);
    connect(m_worker, &FileScanWorker::scanCompleted, this, &DataDiscoveryEngine::onScanCompleted);
    connect(m_worker, &FileScanWorker::scanError, this, &DataDiscoveryEngine::onScanError);

    // 设置优化的扫描配置
    ScanConfig config;
    config.scanPath = scanPath;
    config.includeSubdirectories = includeSubdirs;
    config.scanHiddenFiles = scanHidden;
    config.scanSystemFiles = scanSystem;
    // config.fileExtensions 保持为空，这样会使用排除列表逻辑

    // 设置线程数量（默认为CPU核心数，但可以根据需要调整）
    config.threadCount = qMax(1, qMin(QThread::idealThreadCount(), 8)); // 限制最大8个线程

    // qDebug() << "启动多线程扫描，线程数:" << config.threadCount;

    m_worker->setScanConfig(config);

    // 获取当前规则的副本并传递给worker
    QList<OptimizedScanRule> currentRules;
    {
        QMutexLocker locker(&m_rulesMutex);
        currentRules = m_scanRules;
    }

    // qDebug() << "传递给扫描器的规则数量:" << currentRules.size();
    for (const auto& rule : currentRules) {
        // qDebug() << "  规则:" << rule.name << "启用:" << rule.enabled;
    }

    m_worker->setScanRules(currentRules);

    // 清空之前的结果
    clearScanResults();

    m_isScanning = true;
    m_scanProgress = 0;
    m_currentScanFile.clear();

    emit isScanningChanged();
    emit scanProgressChanged();
    emit currentScanFileChanged();

    m_workerThread->start();
}



void DataDiscoveryEngine::stopScan()
{
    if (!m_isScanning || !m_worker) {
        return;
    }

    m_worker->stopScan();

    if (m_workerThread && m_workerThread->isRunning()) {
        m_workerThread->quit();
        if (!m_workerThread->wait(5000)) {
            m_workerThread->terminate();
            m_workerThread->wait();
        }
    }

    delete m_worker;
    delete m_workerThread;
    m_worker = nullptr;
    m_workerThread = nullptr;

    m_isScanning = false;
    m_scanProgress = 0;
    m_currentScanFile.clear();

    emit isScanningChanged();
    emit scanProgressChanged();
    emit currentScanFileChanged();
}

QVariantList DataDiscoveryEngine::getScanResults() const
{
    QMutexLocker locker(&m_resultsMutex);

    QVariantList results;
    for (const auto& result : m_scanResults) {
        results.append(scanResultItemToVariant(result));
    }
    return results;
}

QVariantList DataDiscoveryEngine::getFilteredResults(int dataType, int riskLevel) const
{
    QMutexLocker locker(&m_resultsMutex);

    QVariantList results;
    for (const auto& result : m_scanResults) {
        bool matchDataType = (dataType == -1) || (static_cast<int>(result.dataType) == dataType);
        bool matchRiskLevel = (riskLevel == -1) || (static_cast<int>(result.riskLevel) == riskLevel);

        if (matchDataType && matchRiskLevel) {
            results.append(scanResultItemToVariant(result));
        }
    }
    return results;
}

void DataDiscoveryEngine::clearScanResults()
{
    QMutexLocker locker(&m_resultsMutex);
    m_scanResults.clear();
    m_currentStatistics = ScanStatistics();
    emit scanStatisticsChanged();
}

// 保存扫描结果实现
bool DataDiscoveryEngine::saveScanResults() const
{
    QString filePath = getScanResultsFilePath();
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        // qDebug() << "无法打开扫描结果文件进行写入:" << file.errorString();
        return false;
    }

    // 创建文件流
    QTextStream stream(&file);
    stream.setEncoding(QStringConverter::Utf8); //设置编码

    // 创建JSON对象
    QJsonObject rootObj;
    QJsonObject statsObj;

    // 添加统计数据
    statsObj["totalFilesScanned"] = m_currentStatistics.totalFilesScanned;
    statsObj["totalSensitiveItems"] = m_currentStatistics.totalSensitiveItems;
    statsObj["piiCount"] = m_currentStatistics.piiCount;
    statsObj["financialCount"] = m_currentStatistics.financialCount;
    statsObj["healthCount"] = m_currentStatistics.healthCount;
    statsObj["businessSecretCount"] = m_currentStatistics.businessSecretCount;
    statsObj["otherCount"] = m_currentStatistics.otherCount;
    statsObj["highRiskCount"] = m_currentStatistics.highRiskCount;
    statsObj["mediumRiskCount"] = m_currentStatistics.mediumRiskCount;
    statsObj["lowRiskCount"] = m_currentStatistics.lowRiskCount;
    statsObj["scanStartTime"] = m_currentStatistics.scanStartTime.toString(Qt::ISODate);
    statsObj["scanEndTime"] = m_currentStatistics.scanEndTime.toString(Qt::ISODate);

    rootObj["statistics"] = statsObj;
    rootObj["lastSaved"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    // 添加详细扫描信息
    QJsonArray scanItemsArray;
    for(const auto& item : m_scanResults){
        scanItemsArray.append(QJsonObject::fromVariantMap(scanResultItemToVariant(item)));
    }
    rootObj["scanItems"] = scanItemsArray;

    // 写入数据
    QJsonDocument doc(rootObj);
    stream << doc.toJson();

    qWarning() << "扫描结果已保存到:" << file.fileName();

    return true;
}

// 加载扫描结果函数实现
bool DataDiscoveryEngine::loadScanResults(){

    QString filePath = getScanResultsFilePath();
    QFile file(filePath);

    // 检查文件是否存在
    if(!file.exists()){
        qWarning() << "扫描结果文件不存在:" << filePath;
        return false;
    }

    // 打开文件进行读取
    if(!file.open(QIODevice::ReadOnly | QIODevice::Text)){
        qWarning() << "无法打开扫描结果文件进行读取:" << file.errorString();
        return false;
    }

    // 读取文件内容
    QTextStream stream(&file);
    stream.setEncoding(QStringConverter::Utf8);
    QString jsonString = stream.readAll();

    // 解析JSON
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8(), &parseError);
    if(parseError.error != QJsonParseError::NoError){
        qWarning() << "解析扫描结果文件失败:" << parseError.errorString();
        return false;
    }

    // 获取JSON对象
    QJsonObject rootObj = doc.object();
    QJsonObject statsObj = rootObj["statistics"].toObject();

    // 恢复统计数据到m_currentStatistics
    m_currentStatistics.totalSensitiveItems = statsObj["totalSensitiveItems"].toInt();
    m_currentStatistics.totalFilesScanned = statsObj["totalFilesScanned"].toInt();
    m_currentStatistics.piiCount = statsObj["piiCount"].toInt();
    m_currentStatistics.financialCount = statsObj["financialCount"].toInt();
    m_currentStatistics.healthCount = statsObj["healthCount"].toInt();
    m_currentStatistics.businessSecretCount = statsObj["businessSecretCount"].toInt();
    m_currentStatistics.otherCount = statsObj["otherCount"].toInt();
    m_currentStatistics.highRiskCount = statsObj["highRiskCount"].toInt();
    m_currentStatistics.mediumRiskCount = statsObj["mediumRiskCount"].toInt();
    m_currentStatistics.lowRiskCount = statsObj["lowRiskCount"].toInt();
    
    if(!statsObj["scanStartTime"].toString().isEmpty()){
        m_currentStatistics.scanStartTime = QDateTime::fromString(statsObj["scanStartTime"].toString(), Qt::ISODate);
    }

    if(!statsObj["scanEndTime"].toString().isEmpty()){
        m_currentStatistics.scanEndTime = QDateTime::fromString(statsObj["scanEndTime"].toString(), Qt::ISODate);
    }
    
    // 加载详细扫描项
    QJsonArray scanItemsArray = rootObj["scanItems"].toArray();
    m_scanResults.clear();
    for (const auto& value : scanItemsArray) {
        QJsonObject itemObj = value.toObject();
        ScanResultItem item;
        item.filePath = itemObj["filePath"].toString();
        item.fileName = itemObj["fileName"].toString();
        item.fileSize = itemObj["fileSize"].toVariant().toLongLong();
        item.ruleName = itemObj["ruleName"].toString();
        item.matchedContent = itemObj["matchedContent"].toString();
        item.lineNumber = itemObj["lineNumber"].toInt();
        item.columnNumber = itemObj["columnNumber"].toInt();
        item.dataType = intToDataType(itemObj["dataType"].toInt());
        item.riskLevel = intToRiskLevel(itemObj["riskLevel"].toInt());
        item.scanTime = QDateTime::fromString(itemObj["scanTime"].toString(), Qt::ISODate);
        m_scanResults.append(item);
    }

    // 发射信号通知页面更新
    emit scanStatisticsChanged();

    qDebug() << "成功加载扫描结果, 共" << m_currentStatistics.totalSensitiveItems << "个敏感数据项";
    
    return true;
}

// 获取扫描结果文件路径
QString DataDiscoveryEngine::getScanResultsFilePath() const
{
    QString appDir = QCoreApplication::applicationDirPath();
    QString dataDir = appDir + "/src/data/";
    return dataDir + "scanResults.json";
}

// 私有槽函数实现
void DataDiscoveryEngine::onScanProgress(int percentage, const QString& currentFile)
{
    m_scanProgress = percentage;
    m_currentScanFile = currentFile;

    emit scanProgressChanged();
    emit currentScanFileChanged();
}

void DataDiscoveryEngine::onScanResultFound(const ScanResultItem& result)
{
    {
        QMutexLocker locker(&m_resultsMutex);
        m_scanResults.append(result);
    }

    emit newSensitiveDataFound(scanResultItemToVariant(result));
}

void DataDiscoveryEngine::onScanCompleted(const ScanStatistics& statistics)
{
    m_currentStatistics = statistics;

    m_isScanning = false;
    m_scanProgress = 100;

    // qDebug() << "扫描已停止";

    // 保存扫描结果
    saveScanResults();

    emit isScanningChanged();
    emit scanProgressChanged();
    emit scanStatisticsChanged();
    emit scanCompleted();

    // 清理工作线程
    if (m_workerThread) {
        m_workerThread->quit();
        m_workerThread->wait();
        delete m_worker;
        delete m_workerThread;
        m_worker = nullptr;
        m_workerThread = nullptr;
    }
}

void DataDiscoveryEngine::onScanError(const QString& error)
{
    m_isScanning = false;
    m_scanProgress = 0;

    emit isScanningChanged();
    emit scanProgressChanged();
    emit scanError(error);

    // 清理工作线程
    if (m_workerThread) {
        m_workerThread->quit();
        m_workerThread->wait();
        delete m_worker;
        delete m_workerThread;
        m_worker = nullptr;
        m_workerThread = nullptr;
    }
}

// 辅助函数实现
QVariantMap DataDiscoveryEngine::scanResultItemToVariant(const ScanResultItem& item) const
{
    QVariantMap variant;
    variant["filePath"] = item.filePath;
    variant["fileName"] = item.fileName;
    variant["fileSize"] = item.fileSize;
    variant["ruleName"] = item.ruleName;
    variant["matchedContent"] = item.matchedContent;
    variant["lineNumber"] = item.lineNumber;
    variant["columnNumber"] = item.columnNumber;
    variant["dataType"] = static_cast<int>(item.dataType);
    variant["dataTypeName"] = dataTypeToString(item.dataType);
    variant["riskLevel"] = static_cast<int>(item.riskLevel);
    variant["riskLevelName"] = riskLevelToString(item.riskLevel);
    variant["scanTime"] = item.scanTime;
    return variant;
}

QVariantMap DataDiscoveryEngine::scanRuleToVariant(const OptimizedScanRule& rule) const
{
    QVariantMap variant;
    variant["name"] = rule.name;
    variant["pattern"] = rule.pattern;
    variant["dataType"] = static_cast<int>(rule.dataType);
    variant["dataTypeName"] = dataTypeToString(rule.dataType);
    variant["riskLevel"] = static_cast<int>(rule.riskLevel);
    variant["riskLevelName"] = riskLevelToString(rule.riskLevel);
    variant["enabled"] = rule.enabled;
    variant["description"] = rule.description;
    return variant;
}

OptimizedScanRule DataDiscoveryEngine::variantToScanRule(const QVariantMap& variant) const
{
    OptimizedScanRule rule;
    rule.name = variant["name"].toString();
    rule.pattern = variant["pattern"].toString();
    rule.dataType = intToDataType(variant["dataType"].toInt());
    rule.riskLevel = intToRiskLevel(variant["riskLevel"].toInt());
    rule.enabled = variant["enabled"].toBool();
    rule.description = variant["description"].toString();

    // 编译规则
    rule.compileRule();

    return rule;
}

QString DataDiscoveryEngine::dataTypeToString(SensitiveDataType type) const
{
    switch (type) {
        case SensitiveDataType::PII:
            return "PII";
        case SensitiveDataType::Financial:
            return "财务";
        case SensitiveDataType::Health:
            return "健康";
        case SensitiveDataType::BusinessSecret:
            return "商业机密";
        case SensitiveDataType::Other:
        default:
            return "其他";
    }
}

QString DataDiscoveryEngine::riskLevelToString(RiskLevel level) const
{
    switch (level) {
        case RiskLevel::High:
            return "高风险";
        case RiskLevel::Medium:
            return "中风险";
        case RiskLevel::Low:
            return "低风险";
        case RiskLevel::None:
        default:
            return "无风险";
    }
}

SensitiveDataType DataDiscoveryEngine::intToDataType(int type) const
{
    switch (type) {
        case 0:
            return SensitiveDataType::PII;
        case 1:
            return SensitiveDataType::Financial;
        case 2:
            return SensitiveDataType::Health;
        case 3:
            return SensitiveDataType::BusinessSecret;
        case 4:
        default:
            return SensitiveDataType::Other;
    }
}

RiskLevel DataDiscoveryEngine::intToRiskLevel(int level) const
{
    switch (level) {
        case 0:
            return RiskLevel::High;
        case 1:
            return RiskLevel::Medium;
        case 2:
            return RiskLevel::Low;
        case 3:
        default:
            return RiskLevel::None;
    }
}

// 规则文件管理函数实现
QString DataDiscoveryEngine::getRulesFilePath() const
{
    QString appDir = QCoreApplication::applicationDirPath();
    QString dataDir = appDir + "/src/data/rules";
    return dataDir + "/scanFilesRules.json";
}

bool DataDiscoveryEngine::ensureDataDirectoryExists() const
{
    QString appDir = QCoreApplication::applicationDirPath();
    QString dataDir = appDir + "/src/data/rules";

    QDir dir;
    if (!dir.exists(dataDir)) {
        if (!dir.mkpath(dataDir)) {
            qWarning() << "无法创建数据目录:" << dataDir;
            return false;
        }
    }
    return true;
}

bool DataDiscoveryEngine::saveRulesToFile() const
{
    if (!ensureDataDirectoryExists()) {
        return false;
    }

    QString filePath = getRulesFilePath();
    QFile file(filePath);

    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "无法打开规则文件进行写入:" << filePath;
        return false;
    }

    QJsonObject rootObj;
    QJsonArray rulesArray;

    {
        QMutexLocker locker(&m_rulesMutex);
        for (const auto& rule : m_scanRules) {
            QJsonObject ruleObj;
            ruleObj["name"] = rule.name;
            ruleObj["pattern"] = rule.pattern;
            ruleObj["dataType"] = static_cast<int>(rule.dataType);
            ruleObj["riskLevel"] = static_cast<int>(rule.riskLevel);
            ruleObj["enabled"] = rule.enabled;
            ruleObj["description"] = rule.description;

            rulesArray.append(ruleObj);
        }
    }

    rootObj["rules"] = rulesArray;
    rootObj["version"] = "1.0";
    rootObj["lastModified"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    QJsonDocument doc(rootObj);
    QTextStream stream(&file);
    stream.setEncoding(QStringConverter::Utf8);
    stream << doc.toJson();

    // qDebug() << "规则已保存到文件:" << filePath;
    return true;
}

bool DataDiscoveryEngine::loadRulesFromFile()
{
    QString filePath = getRulesFilePath();
    QFile file(filePath);

    if (!file.exists()) {
        // qDebug() << "规则文件不存在:" << filePath;
        return false;
    }

    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "无法打开规则文件进行读取:" << filePath;
        return false;
    }

    QTextStream stream(&file);
    stream.setEncoding(QStringConverter::Utf8);
    QString jsonString = stream.readAll();

    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8(), &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        qWarning() << "解析规则文件失败:" << parseError.errorString();
        return false;
    }

    QJsonObject rootObj = doc.object();
    QJsonArray rulesArray = rootObj["rules"].toArray();

    {
        QMutexLocker locker(&m_rulesMutex);
        m_scanRules.clear();

        for (const auto& value : rulesArray) {
            QJsonObject ruleObj = value.toObject();

            OptimizedScanRule rule;
            rule.name = ruleObj["name"].toString();
            rule.pattern = ruleObj["pattern"].toString();
            rule.dataType = intToDataType(ruleObj["dataType"].toInt());
            rule.riskLevel = intToRiskLevel(ruleObj["riskLevel"].toInt());
            rule.enabled = ruleObj["enabled"].toBool();
            rule.description = ruleObj["description"].toString();

            // 编译规则
            rule.compileRule();

            m_scanRules.append(rule);
        }
    }

    // qDebug() << "从文件加载了" << rulesArray.size() << "条规则";
    return true;
}

// 缓存管理方法实现
QVariantMap DataDiscoveryEngine::getCacheStatistics() const
{
    QVariantMap stats;

    // FileProcessTask的静态缓存统计
    int mimeHits, mimeMisses, mimeSize;
    int regexHits, regexMisses, regexSize;

    FileProcessTask::s_mimeTypeCache.getStatistics(mimeHits, mimeMisses, mimeSize);
    FileProcessTask::s_regexMatchCache.getStatistics(regexHits, regexMisses, regexSize);

    stats["fileTypeCacheHits"] = mimeHits;
    stats["fileTypeCacheMisses"] = mimeMisses;
    stats["fileTypeCacheSize"] = mimeSize;
    stats["fileTypeCacheHitRate"] = FileProcessTask::s_mimeTypeCache.hitRate();

    stats["regexCacheHits"] = regexHits;
    stats["regexCacheMisses"] = regexMisses;
    stats["regexCacheSize"] = regexSize;
    stats["regexCacheHitRate"] = FileProcessTask::s_regexMatchCache.hitRate();

    // FileScanWorker的缓存统计（如果存在）
    if (m_worker) {
        int workerHits, workerMisses, workerSize;
        m_worker->m_fileTypeCache.getStatistics(workerHits, workerMisses, workerSize);
        stats["workerFileTypeCacheHits"] = workerHits;
        stats["workerFileTypeCacheMisses"] = workerMisses;
        stats["workerFileTypeCacheSize"] = workerSize;
        stats["workerFileTypeCacheHitRate"] = m_worker->m_fileTypeCache.hitRate();
    }

    stats["lastUpdated"] = QDateTime::currentDateTime();

    return stats;
}

void DataDiscoveryEngine::clearAllCaches()
{
    // 清理静态缓存
    FileProcessTask::s_mimeTypeCache.clear();
    FileProcessTask::s_regexMatchCache.clear();

    // 清理worker缓存
    if (m_worker) {
        m_worker->m_fileTypeCache.clear();
    }

    // qDebug() << "所有缓存已清理";
}

void DataDiscoveryEngine::optimizeCaches()
{
    // 这里可以实现缓存优化逻辑，比如：
    // 1. 根据命中率调整缓存大小
    // 2. 预热常用的缓存项
    // 3. 清理过期的缓存项

    // qDebug() << "缓存优化完成";

    // 输出优化后的统计信息
    QVariantMap stats = getCacheStatistics();
    // qDebug() << "文件类型缓存命中率:" << stats["fileTypeCacheHitRate"].toDouble() * 100 << "%";
    // qDebug() << "正则匹配缓存命中率:" << stats["regexCacheHitRate"].toDouble() * 100 << "%";
}

#include "DataDiscoveryEngine.moc"
